'use client'

import { MoneyIcon } from '@/app/(main)/history/misc/icons';
import { SmallSpinner } from '@/components/icons';
import { <PERSON>ton, Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, Input } from '@/components/ui'
import { useAuth } from '@/contexts';
import { cn } from '@/utils/classNames'
import { format } from 'date-fns';
import { Banknote, Calendar, CreditCardIcon, XIcon } from 'lucide-react';
import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation';
import { UseEndBreak, UseStartBreak } from '@/app/(main)/dashboard/misc/api';


const BreakPage = () => {
  const buttonRef = React.useRef<HTMLButtonElement>(null);
  const { isAuthenticated, isLoading, shiftStatus, startBreak: authStartBreak } = useAuth();
  const router = useRouter()
  const { mutate: startBreak, isPending: isStartingShift } = UseStartBreak()
  const { mutate: endBreak, isPending: isEndingShift } = UseEndBreak()

  const submit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!isLoading && !isAuthenticated) {
      router.replace('/login');
      return;
    }
    else if (!isLoading && !shiftStatus.ongoing_break) {
      startBreak()
      authStartBreak()
    } else if (!isLoading && shiftStatus.ongoing_break && !shiftStatus.shift_ended) {
      endBreak()
      router.replace('/login');
    }
  }

  useEffect(() => {
    // if (!isLoading && !isAuthenticated) {
    //   router.replace('/login');
    //   return;
    // }

  }, [isLoading, isAuthenticated, router])


  return (
    <Dialog modal={true} open={true} >
      <DialogContent
        aria-label={"Confirm Cash Payment"}
        className={cn(
          'rounded-[10px]',
          'my-6 bg-[#1C1C1C] !px-0 !pb-0 !pt-4 !max-w-[500px]'
        )}
        style={{
          width: '92.5%',
          minWidth: '300px',
        }}
        onPointerDownOutside={() => router.replace('/dashboard')}

      >
        <DialogTitle className=' px-6 md:px-8 text-2xl font-semibold'>Take break</DialogTitle>

        <DialogHeader className={cn('max-sm:sticky top-0 z-10 px-6 md:px-8')}>

        </DialogHeader>


        <form onSubmit={submit}>
          <div className={cn('flex flex-col gap-3 p-6 rounded-xl')}>
            <p className='flex items-center gap-1.5 text-sm text-[#A2A2A2]'>
              Current date and time
            </p>
            <p className='flex items-center gap-2 font-medium text-sm w-full bg-[#181818] p-2 px-4 rounded-md text-[#D8D8DF]'>
              <Calendar className='text-[#D8D8DF]' size={16} />
              {format(new Date(), 'dd/MM/yyyy - hh:mm:ss a')}
            </p>
          </div>




        </form>
        <DialogFooter className='flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8 text-[0.9rem]'>

          <Button className='py-6 w-full' ref={buttonRef}>
            {
              !shiftStatus.ongoing_break ?
                'Start break'
                :
                isStartingShift ?
                  'Starting break...'
                  :
                  shiftStatus.ongoing_break && !shiftStatus.shift_ended ?
                    'End break'
                    :
                    isEndingShift ?
                      'Ending break...'
                      :
                      'Break ended'
            }
            {
              (isStartingShift || isEndingShift) &&
              <SmallSpinner color='black' className='ml-2' />
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default BreakPage