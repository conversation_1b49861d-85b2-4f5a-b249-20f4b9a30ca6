'use client'
import { managementAxios } from "@/lib/axios";
import { useCompanyStore } from "@/stores";
import { tokenStorage } from "@/utils/auth";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";

const endShift = async (cash_in_hand: number) => {
    const shiftTime = await managementAxios.post(`/core/end_sales_shift/`, { cash_in_hand });
    return shiftTime.data
}

const UseEndShift = () => {
    const router = useRouter();
    const { clearCompanyInfo } = useCompanyStore()
    return useMutation({
        mutationFn: endShift,
        onSuccess() {
            tokenStorage.clearToken();
            clearCompanyInfo()
            router.push('/login')
        },
    })
}

export default UseEndShift;