'use client'

import { managementAxios } from "@/lib/axios";
import { useMutation } from "@tanstack/react-query";

const startShift = async (cash_in_hand: number) => {
    const shiftTime = await managementAxios.post(`/core/start_sales_shift/`, {cash_in_hand});
    return shiftTime.data
}

const UseStartShift = () => {
    return useMutation({
        mutationFn: startShift,
    })
}

export default UseStartShift;