'use client'
import { managementAxios } from '@/lib/axios';

import { useMutation } from '@tanstack/react-query';

import { AxiosError } from 'axios';

import { useRouter } from 'next/navigation';

import { loginDataType } from '../../login/page';



type LoginResponse = {
  id: string;
  access: string;
  refresh: string;
  message: string;
  status_code: string;
  sales_user_role: string;
  branch: string,
  company: string
  username: string;
  first_name:string;
  last_name:string
};

export const loginApi = {
  login: async (data: loginDataType): Promise<LoginResponse> => {
    const response = await managementAxios.post<LoginResponse>(`/core/sales_login/`, data);
    return response.data;
  },
};


export const UseLoginMutation = () => {
  const router = useRouter();

  return useMutation<LoginResponse, AxiosError<LoginResponse>, loginDataType>({
    mutationFn: loginApi.login,
    onError: (error, variable) => {
      if (error.response?.status === 401) {
        const responseData = error.response.data;
        if (responseData.status_code[0] === "102" && responseData.message[0] === "User should reset password") {
          router.push('/reset-passcode');
          if (window && typeof window !== 'undefined') {
            localStorage.setItem('newUser', JSON.stringify(variable));
          }
        }
      }
    },
  });
};