'use client'
import React, { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts';
import { Paybox360 } from '@/components/icons';
import toast from 'react-hot-toast';
import { useCompanyStore } from '@/stores';
import dynamic from 'next/dynamic';

const MQTTComponent = dynamic(() => import('./MQTT'), { ssr: false });

const ProtectedRoutesLayout = ({ children }:
    { children: React.ReactNode }) => {
    const { isAuthenticated, isLoading, shiftStatus, isBreakVerified } = useAuth();
    const router = useRouter();
    const { first_name } = useCompanyStore()

    const handleRouting = useCallback(() => {
        if (!isLoading && !isAuthenticated) {
            router.replace('/login');
            return;
        }

        if (!shiftStatus) return;

        const { shift_started, shift_ended, ongoing_break } = shiftStatus;

        if (shift_started && !shift_ended) {
            if (ongoing_break) {
                if (!isBreakVerified) {
                    toast.error('Shift is on break. Please login again to continue');
                    router.replace('/login?break=true');
                }
                toast.error('Shift is on break. Please end the break to continue');
                router.replace('/onboard/break');
            } else {
                // router.replace('/dashboard');
            }
        } else if (!shift_started && !shift_ended) {
            router.replace('/onboard/shift');
        } else if (shift_started && shift_ended) {
            toast.error('Shift has ended. Please start a new shift to continue');
            router.replace('/login');
        }
    }, [isAuthenticated, shiftStatus, router, isLoading]);

    useEffect(() => {
        if (!isLoading) {
            handleRouting();
        }
    }, [isLoading, handleRouting]);

    if (isLoading) {
        return (
            <div className='h-screen w-screen flex items-center justify-center'>
                <Paybox360 className='animate-pulse' />
            </div>
        );
    }

    if (!isAuthenticated) {
        return null;
    }

    const shouldRenderChildren = isAuthenticated && shiftStatus && !shiftStatus.ongoing_break;

    return (
        <>
            {/* <MQTTComponent /> */}
            {shouldRenderChildren && children}
        </>
    );
};

export default ProtectedRoutesLayout;