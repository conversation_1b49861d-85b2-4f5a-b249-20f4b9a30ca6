import React from "react";

import { cn } from '@/utils/classNames'
import { Skeleton } from "@/components/ui";



export const ProductItemCardSkeleton = () => {

    
    return (
        <article
            className={cn("flex flex-col gap-1.5 p-4 rounded-xl aspect-[5/3] focus:outline-none outline-none border-2 border-transparent bg-[#1C1C1C]",)}
            // style={{ backgroundColor: bg }}
        >
            <Skeleton className="w-24 h-5 font-medium" />
            <Skeleton className="w-24 h-3 font-medium" />
            <Skeleton className="w-12 h-5 font-medium" />


            <footer className="flex items-center justify-end gap-2 mt-4">
                <Skeleton className="w-6 h-5" />
                <Skeleton className="w-6 h-5" />
                <Skeleton className="w-6 h-5" />


            </footer>
        </article>
    );
}

