import React from 'react';
import {
    <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alog<PERSON><PERSON>le, DialogBody, DialogFooter, DialogClose,
    Button
} from '@/components/ui';
import { Button as CoreButton } from "@/components/ui";
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { SingleMerchantAccountDetailDTO } from '../api/postCollectMerchantAccountDetail';
import { XIcon } from 'lucide-react';




interface SelectMerchantAccountProps {
    open: boolean;
    onClose: () => void;
    accounts: SingleMerchantAccountDetailDTO[];
    selected: string; // account_number
    onChange: (account_number: string) => void;
    onContinue: () => void;
    isLoading: boolean;
}

const SelectMerchantAccount: React.FC<SelectMerchantAccountProps> = ({ open, onClose, accounts, selected, onChange, onContinue, isLoading }) => {
    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="bg-[#1C1C1C] rounded-[10px]">
                <DialogHeader className=''>
                    <div className='w-full flex justify-between items-center'>
                        <div className='text-white text-lg font-medium'>Select account</div>
                        <Button
                            className="bg-[#D9D9D91A] p-0 rounded-full h-9 w-9"
                            size={"sm"}
                            variant={"unstyled"}
                            onClick={onClose}
                        >
                            <XIcon size={20} />
                        </Button>
                    </div>
                </DialogHeader>
                <DialogBody className='p-0'>
                    <div className="bg-[#D9D9D91A] rounded-2xl p-6 w-full max-w-xl mx-auto">
                        <div className="text-gray-200 text-lg mb-4">Bank account details</div>
                        {accounts && accounts.length > 0 ? (
                            <RadioGroup value={selected} onValueChange={onChange} className="space-y-4">
                                {(accounts ?? []).map((acc, idx) => (
                                    <div key={acc.account_number} className="flex items-start gap-3 py-3">
                                        <RadioGroupItem value={acc.id} id={`acc-${acc.account_number}`} className="mt-1 border-white" />
                                        <label htmlFor={`acc-${acc.account_number}`} className="flex-1 cursor-pointer">
                                            <div className="text-gray-200 text-base font-medium mb-1">{acc.account_name}</div>
                                            <div className="text-gray-100 text-lg font-semibold tracking-wide">
                                                {acc.account_number} <span className="text-gray-300 font-normal">– {acc.bank_name}</span>
                                            </div>
                                        </label>
                                    </div>
                                ))}
                            </RadioGroup>
                        ) : (
                            <div className="text-center py-8">
                                <div className="text-gray-300 text-base mb-2">No accounts available</div>
                                <div className="text-gray-400 text-sm">Contact or Inform admin to add accounts</div>
                            </div>
                        )}
                    </div>
                </DialogBody>
                <DialogFooter className=''>
                    <CoreButton
                        className="w-full  bg-white text-black px-8 py-3 rounded-md text-base font-medium hover:bg-gray-200 disabled:bg-gray-600 disabled:text-gray-400 disabled:cursor-not-allowed"
                        onClick={() => {
                            onContinue();
                            onClose();
                        }}
                        disabled={!selected || isLoading || !accounts || accounts.length === 0}
                    >
                        {isLoading ? 'Loading...' : 'Continue'}
                    </CoreButton>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default SelectMerchantAccount; 