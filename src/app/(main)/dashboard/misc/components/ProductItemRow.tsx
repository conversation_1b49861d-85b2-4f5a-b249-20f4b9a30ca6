import React from "react";
import { TCartItem, TProduct } from "../types";
import { cn } from '@/utils/classNames'
import { convertNumberToNaira } from "@/utils/currency";

interface ProductItemProps {
    product: TProduct;
    onAddToCart: (product: TProduct) => void;
    onRemoveFromCart: (id: string) => void;
    isFocused: boolean;
    cartItems: TCartItem[];
}

export const ProductItemRow: React.FC<ProductItemProps> = ({ product, onAddToCart, onRemoveFromCart, isFocused, cartItems }) => {
    const cartItem = cartItems.find(item => item.item_id === product.item_id) || {} as TCartItem;

    return (
        <article
            className={cn(
                "grid grid-cols-[subgrid] col-span-3 items-center justify-between gap-1.5 p-4 focus:outline-none outline-none",
                {
                    'bg-background': isFocused
                }
            )}
        >
            <p className="truncate font-normal text-sm">
                {product.item}
            </p>
            <div className="flex items-center justify-end gap-2">
                <button
                    onClick={() => onRemoveFromCart(product.item_id)}
                    aria-label={`Remove ${product.item} from cart`}
                    className="flex items-center justify-center w-[1.325rem] h-5 p-0 font-light text-sm bg-[#F9F9F933] rounded-md"
                >
                    -
                </button>
                <p className="text-[0.8rem] min-w-[4ch] text-center bg-black/80 px-1.5 py-[0.0625rem] rounded-md">
                    {cartItem.quantity || 0}
                </p>
                <button
                    onClick={() => onAddToCart(product)}
                    aria-label={`Add ${product.item} to cart`}
                    className="flex items-center justify-center w-[1.325rem] h-5 p-0 font-light text-sm bg-[#F9F9F933] rounded-md"
                >
                    +
                </button>
            </div>
            <p className="text-sm justify-self-end">
                {convertNumberToNaira(parseInt(product.selling_price))}
            </p>
        </article>
    );
};