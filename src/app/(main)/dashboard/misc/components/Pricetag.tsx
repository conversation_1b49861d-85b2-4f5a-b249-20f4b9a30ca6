import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui";
import { cn } from "@/utils/classNames";
import { CheckCircle, Circle } from "lucide-react";
import React from "react";

export interface Itag {
  name: string;
  id: string;
}
interface Iprops {
  tags: Itag[];
  value: Itag;
  onValueChange: (v: Itag) => void;
  className?: string
}
export const PriceTag: React.FC<Iprops> = ({ tags, value, onValueChange, className }) => {
  return (
    <div>
      <Popover>
        <PopoverTrigger className={cn(className && className, "flex items-center justify-between gap-2 rounded-md bg-[#27272A] px-3 w-[150px] py-2 text-xs font-medium")}>
          <span className="text-main-solid capitalize ">{value.name}</span>
          <span>
            <svg
              fill="none"
              height="20"
              viewBox="0 0 20 20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M12.3569 11.5237C11.0986 12.782 9.08445 12.824 7.77571 11.6495L7.64289 11.5237L4.41066 8.09082C4.08522 7.76538 4.08522 7.23775 4.41066 6.91231C4.71106 6.61191 5.18376 6.5888 5.51067 6.84299L5.58917 6.91231L8.82141 10.3452C9.43802 10.9618 10.4176 10.9942 11.0724 10.4425L11.1784 10.3452L14.4107 6.91231C14.7361 6.58687 15.2637 6.58687 15.5892 6.91231C15.8896 7.21271 15.9127 7.68541 15.6585 8.01232L15.5892 8.09082L12.3569 11.5237Z"
                fill="#ffffff"
                fillRule="evenodd"
              />
            </svg>
          </span>
        </PopoverTrigger>

        <PopoverContent
          align="end"
          className="max-h-max !p-2 w-[200px] border-card-border"
        >
          {tags.map((v, key) => (
            <div
              className="flex justify-between items-center text-sm py-2 border-b"
              key={key}
              onClick={() => onValueChange(v)}
            >
              <span className="block capitalize">{v.name}</span>
              {v.name === value.name ? (
                <CheckCircle size={16} />
              ) : (
                <Circle size={16} />
              )}
            </div>
          ))}
        </PopoverContent>
      </Popover>
    </div>
  );
};
