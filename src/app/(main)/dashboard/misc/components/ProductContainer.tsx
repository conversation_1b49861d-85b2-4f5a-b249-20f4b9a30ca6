import React, { useEffect, useRef, useState } from "react";
import { useGetBranchDetails } from "../api/getBranchInfo";
import { TCartItem, TProduct } from "../types";
import { ProductItemCard } from "./ProductItemCard";
import { ProductItemCardSkeleton } from "./ProductItemCardSkeleton";

interface ProductContainerProps {
    products: TProduct[] | undefined;
    isFetchingProducts: boolean;
    onAddToCart: (product: TProduct) => void;
    onRemoveFromCart: (id: string) => void;
    cartItems: TCartItem[];
    branch: string;
    company: string;
    loadMoreDataObserver: JSX.Element
}

export const ProductContainer = React.forwardRef<HTMLUListElement, ProductContainerProps>((props, ref) => {
    const { products, isFetchingProducts, onAddToCart, onRemoveFromCart, cartItems, company, branch, loadMoreDataObserver } = props;
    const [focusedIndex, setFocusedIndex] = useState(-1);
    const itemRefs = useRef<(HTMLElement | null)[]>([]);

    const { data: branchDetails } = useGetBranchDetails(company, branch)
    const branchInfo = branchDetails?.data?.branch;

    useEffect(() => {
        if (focusedIndex !== -1 && itemRefs.current[focusedIndex]) {
            itemRefs.current[focusedIndex]?.focus();
        }
    }, [focusedIndex]);


    const handleKeyDown = (event: React.KeyboardEvent<HTMLUListElement>) => {
        if (event.key === 'ArrowDown' || event.key === 'ArrowRight' && products) {
            setFocusedIndex(prev => Math.min(prev + 1, products?.length! - 1));
        } else if (event.key === 'ArrowUp' || event.key === 'ArrowLeft') {
            setFocusedIndex(prev => Math.max(prev - 1, 0));
        }
    };


    return (
        <ul
            onKeyDown={handleKeyDown}
            className="grid grid-cols-2 xs:grid-cols-[repeat(auto-fill,minmax(120px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(140px,1fr))] md:grid-cols-[repeat(auto-fill,minmax(160px,1fr))] lg:grid-cols-[repeat(auto-fill,minmax(180px,1fr))] xl:grid-cols-[repeat(auto-fill,minmax(200px,1fr))] h-max max-h-full overflow-y-auto [scrollbar-width:thin] p-2 sm:p-3 md:p-4 lg:p-5 rounded-lg sm:rounded-xl gap-1.5 sm:gap-2 md:gap-3 lg:gap-4 border-2 border-transparent focus:border-blue-600 focus-within:border-blue-600 outline-none"
            tabIndex={0}
        >
            {
                isFetchingProducts
                    ?
                    Array.from({ length: 20 }).map((_, index) => (
                        <ProductItemCardSkeleton key={index} />
                    ))
                    : products?.map((product, index) => (
                        <ProductItemCard
                            key={product.item_id}
                            product={product}
                            onAddToCart={onAddToCart}
                            onRemoveFromCart={onRemoveFromCart}
                            isFocused={index === focusedIndex}
                            cartItems={cartItems}
                            sell_without_inventory={branchInfo?.sell_without_inventory}
                            ref={(el) => { itemRefs.current[index] = el; }}
                        />
                    ))
            }
            {loadMoreDataObserver}
        </ul>
    );
});

ProductContainer.displayName = 'ProductContainer';