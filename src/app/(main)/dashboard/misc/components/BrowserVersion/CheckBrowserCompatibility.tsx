'use client'

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, Di<PERSON>Header } from '@/components/ui';
import { cn } from '@/utils/classNames';
import { AlertCircle, XIcon } from 'lucide-react';
import React from 'react';

interface Props {
    isModalOpen: boolean;
    closeModal: () => void;
    browserVersion: number | null;
    isOldVersion: boolean | null;
}
export const CheckBrowserCompatibility: React.FC<Props> = ({
    isModalOpen,
    closeModal,
    browserVersion,
    isOldVersion
}) => {
    return (
        <Dialog modal={true} open={isModalOpen}>
            <DialogContent
                aria-label={"Confirm Others Payment"}
                className={cn(
                    'rounded-[10px]',
                    'my-6 bg-[#1C1C1C] !px-0 !pb-0 !pt-4 !max-w-[500px]'
                )}
                style={{
                    width: '92.5%',
                    minWidth: '300px',
                }}
                onPointerDownOutside={closeModal}
            >

                <DialogHeader className={cn('max-sm:sticky top-0 z-10 px-6 md:px-8')}>
                    <div className='flex items-center justify-between'>
                        <h5 className='text-base font-medium '>Browser update instructions</h5>
                        <Button className='bg-[#D9D9D91A] p-0 rounded-full h-9 w-9' size={'sm'} variant={'unstyled'} onClick={closeModal}>
                            <XIcon size={20} />
                        </Button>
                    </div>
                </DialogHeader>

                <DialogFooter className='flex items-center justify-end gap-4 bg-[#D9D9D91A] w-full rounded-2xl p-5 py-6 md:px-8'>


                    <div className="p-4 max-w-md mx-auto bg-[#313131] rounded-xl shadow-md overflow-hidden md:max-w-2xl">
                        <div className="md:flex">
                            <div className="p-4">
                                {browserVersion !== null ? (
                                    <>
                                        <p className="mt-2 text-white">
                                            Detected Chrome version: <span className="font-bold">{browserVersion}</span>
                                        </p>
                                        {isOldVersion !== null && (
                                            <div className="mt-2">
                                                <p className="text-white">
                                                    This version is{' '}
                                                    <span className={`font-bold ${isOldVersion ? 'text-red-500' : 'text-green-500'}`}>
                                                        {isOldVersion ? 'from 2022 or earlier' : 'newer than 2022'}
                                                    </span>
                                                </p>

                                            </div>
                                        )}
                                    </>
                                ) : (
                                    <p className="mt-2 text-gray-500">Chrome version could not be detected. You may be using a different browser.</p>
                                )}


                                <div className="mt-4 p-4 rounded-md">
                                    <h3 className="font-bold text-lg mb-2 flex items-center">
                                        <AlertCircle className="mr-2" />
                                        Manual Update Instructions
                                    </h3>
                                    <ol className="list-decimal list-inside space-y-2">
                                        <li>Open Google Chrome</li>
                                        <li>Click on the three dots in the top-right corner</li>
                                        <li>Go to Help {'>'} About Google Chrome</li>
                                        <li>Chrome will automatically check for updates</li>
                                        <li>If an update is available, click &quot;Relaunch&quot; to install</li>
                                        <li>If no update option appears, your Chrome is up to date</li>
                                    </ol>
                                    <p className="mt-4 text-sm">
                                        If you&apos;re still having issues, visit the{' '}
                                        <a
                                            href="https://www.google.com/chrome/"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:underline"
                                        >
                                            official Chrome download page
                                        </a>{' '}
                                        to manually download and install the latest version.
                                    </p>
                                </div>

                            </div>
                        </div>
                    </div>

                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
