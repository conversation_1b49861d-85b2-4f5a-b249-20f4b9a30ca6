import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui';
import { <PERSON>ton, Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose, DialogBody } from '@/components/ui';
import { TCartItem } from '../types';
import toast from 'react-hot-toast';

interface DiscountModalProps {
    isOpen: boolean;
    onClose: () => void;
    item: TCartItem | null;
    onApplyDiscount: (itemId: string, discountValue: number) => void;
}

export const DiscountModal: React.FC<DiscountModalProps> = ({
    isOpen,
    onClose,
    item,
    onApplyDiscount,
}) => {
    const [discountValue, setDiscountValue] = useState(item?.item_discount_value?.toString() || '');

    useEffect(() => {
        setDiscountValue(item?.item_discount_value?.toString() || '');
    }, [item]);

    const handleApplyDiscount = () => {
        if (!item) return;

        const discount = parseFloat(discountValue);
        const maxDiscount = Number(item.selling_price) * item.quantity;

        if (isNaN(discount) || discount < 0) {
            toast.error('Please enter a valid discount amount');
            return;
        }

        if (discount > maxDiscount) {
            toast.error(`Discount cannot exceed item total (₦${maxDiscount.toFixed(2)})`);
            return;
        }

        onApplyDiscount(item.item_id, discount);
        setDiscountValue('');
        onClose();
    };

    const handleClose = () => {
        setDiscountValue('');
        onClose();
    };

    if (!item) return null;

    const itemTotal = Number(item.selling_price) * item.quantity;

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className=" bg-[#1C1C1C] border-gray-700">
                <div className='flex justify-between items-center'>
                    <DialogTitle className="text-white">Apply Discount</DialogTitle>
                    <DialogClose className="text-gray-400 hover:text-white">Close</DialogClose>
                </div>
                <div className='p-0'>
                    <div className="space-y-4">
                        <div className="text-sm text-gray-300">
                            <p><strong>Item:</strong> {item.item}</p>
                            <p><strong>Quantity:</strong> {item.quantity}</p>
                            <p><strong>Unit Price:</strong> ₦{Number(item.selling_price).toFixed(2)}</p>
                            <p><strong>Item Total:</strong> ₦{itemTotal.toFixed(2)}</p>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                                Discount Amount (₦)
                            </label>
                            <Input
                                type="number"
                                placeholder="Enter discount amount"
                                value={discountValue}
                                onChange={(e) => setDiscountValue(e.target.value)}
                                min="0"
                                max={itemTotal}
                                step="0.01"
                                className="w-full bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                            />
                            <p className="text-xs text-gray-400 mt-1">
                                Maximum discount: ₦{itemTotal.toFixed(2)}
                            </p>
                        </div>
                        <div className="flex gap-3 pt-4">
                            <Button
                                variant="outline"
                                onClick={handleClose}
                                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
                            >
                                Cancel
                            </Button>
                            {item?.item_discount_value && item.item_discount_value > 0 && (
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        onApplyDiscount(item.item_id, 0);
                                        onClose();
                                    }}
                                    className="flex-1 border-red-600 text-red-400 hover:bg-red-900"
                                >
                                    Remove Discount
                                </Button>
                            )}
                            <Button
                                onClick={handleApplyDiscount}
                                className="w-full bg-[#F9F9F9] disabled:bg-muted  disabled:text-white/50 disabled:cursor-not-allowed text-black p-3 rounded-xl"
                                disabled={!discountValue || parseFloat(discountValue) <= 0}
                            >
                                {item?.item_discount_value && item.item_discount_value > 0 ? 'Update Discount' : 'Apply Discount'}
                            </Button>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}; 