import React, { forwardRef, useEffect, useRef, useState } from 'react';
import {  Subcategory } from '../types';
import ProductCategoriesItem from './ProductCategoriesItem';
// import { Button } from '@/components/ui';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface SubCategoriesListProps {
    categories: Subcategory[];
    isLoading: boolean;
    selectedCategory: string | null;
    onCategorySelect: (category: { name: string, id: string }) => void;
}

const SubCategoriesList = forwardRef<HTMLDivElement, SubCategoriesListProps>(
    ({ categories, selectedCategory, onCategorySelect }, ref) => {
        const [focusedIndex, setFocusedIndex] = useState<number>(-1);
        const itemRefs = useRef<(HTMLElement | null)[]>([]);
        const scrollContainerRef = useRef(null);
        const scrollLeft = () => {
            if (scrollContainerRef.current) {
                (scrollContainerRef.current as any).scrollBy({
                    left: -300, // Adjust based on card width
                    behavior: 'smooth',
                });
            }
        };

        // Scroll the container by a fixed amount to the right
        const scrollRight = () => {
            if (scrollContainerRef.current) {
                (scrollContainerRef.current as any).scrollBy({
                    left: 300, // Adjust based on card width
                    behavior: 'smooth',
                });
            }
        };
        useEffect(() => {
            if (focusedIndex !== -1 && itemRefs.current[focusedIndex]) {
                itemRefs.current[focusedIndex]?.focus();
            }
        }, [focusedIndex]);

        const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
            if (categories) {
                switch (event.key) {
                    case 'ArrowUp':
                    case 'ArrowLeft':
                        setFocusedIndex(prev => (prev > 0 ? prev - 1 : categories.length));
                        break;
                    case 'ArrowDown':
                    case 'ArrowRight':
                        setFocusedIndex(prev => (prev < categories.length ? prev + 1 : 0));
                        break;
                    case 'Enter':
                        onCategorySelect(categories[focusedIndex - 1]);
                        break;
                }
            }
        };

        return (
            <div className='flex items-center space-x-2'>
            <div
                className="sell-category flex items-center rounded-lg gap-x-3 basis-[94%] grow overflow-x-auto  scroll-smooth snap-mandatory snap-x mb-0 p-2 pb-2.5 border-[1.75px] border-transparent focus:border-blue-600 focus-within:border-blue-600 !outline-none"
                ref={scrollContainerRef}
                tabIndex={0}
                onKeyDown={handleKeyDown}
            >
                {
                    [{ name: "All Subs", id: '' }, ...categories!]?.map((category, index) => (
                        <ProductCategoriesItem
                            category={category.name === "All Subs" ? { name: 'All Subs', id: '' } : category}
                            isActive={selectedCategory === category.id || focusedIndex === index}
                            key={`${category}-${index}`}
                            label={category.name}
                            ref={(el) => { itemRefs.current[index] = el; }}
                            onCategorySelect={onCategorySelect}
                            type="sub"
                        />
                        
                    ))}
            </div>
            <div className="flex p-2 pb-2.5 space-x-2 justify-center items-center">
                <button className='bg-[#E6ECFE] max-w-max !py-1 !px-2 !rounded-md' onClick={scrollLeft}>
                    <ChevronLeft color='#032282' />
                </button>
                <button className='bg-[#E6ECFE] max-w-max !py-1 !px-2 !rounded-md' onClick={scrollRight}>
                    <ChevronRight color='#032282' />
                </button>
            </div>
        </div>
        );
    }
);

export default SubCategoriesList;
SubCategoriesList.displayName = "SubcategoriesList"
