'use client'
import { AxiosError } from 'axios';
import { authAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';

type FetchAccountNameDTO = {
    account_number: string;
    bank_code: string;
};

type ApiResponse = {
    status: string;
    status_code: number;
    data: {
        account_name: string;
    };
    errors: any;
};

const fetchAccountName = async (data: FetchAccountNameDTO): Promise<ApiResponse> => {
    const response = await authAxios.post<ApiResponse>(
        `/accounts/fetch_account_name/`,
        data
    );
    return response.data;
};

export const useFetchAccountName = () => {
    return useMutation<ApiResponse, AxiosError<ApiResponse>, FetchAccountNameDTO>({
        mutationFn: fetchAccountName,
    });
};
