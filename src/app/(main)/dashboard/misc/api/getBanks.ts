'use client'
import { authAxios } from '@/lib/axios';
import { useQuery } from '@tanstack/react-query';

type Bank = {
    name: string;
    bank_code: string;
};

type BankListsResponse = {
    status: string;
    status_code: number;
    data: {
        bank: Bank[];
    };
    errors: any;
};

const getBanks = async (): Promise<BankListsResponse> => {
    const response = await authAxios.get<BankListsResponse>('/accounts/bank_list/');
    return response.data;
};

export const useGetBanks = () => {
    return useQuery<BankListsResponse>({
        queryKey: ['banks'],
        queryFn: getBanks,
    });
};
