'use client'
import { AxiosError } from 'axios';

import { managementAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';

type ApiResponse = {
    status: string;
    status_code: number;
    data: {
        customer: string,
        location: string,
        default: boolean,
    };
    errors: any;
};

type AddLocationDTO = {
    customer: string,
    location: string,
    default: boolean,
}

const addLocation = async (data: AddLocationDTO): Promise<ApiResponse> => {
    const response = await managementAxios.post<ApiResponse>(`/api/v1/sales/customers/add_location/`, data);
    return response.data;
}

export const useAddLocation = () => {
    return useMutation<ApiResponse, AxiosError<ApiResponse>, AddLocationDTO>({
        mutationFn: addLocation,
    });
};
