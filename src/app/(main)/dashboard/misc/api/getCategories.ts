import { useQuery } from '@tanstack/react-query';

import { managementAxios } from '@/lib/axios';
import { useCompanyStore } from '@/stores';
import { ISubcategory, TCategory, TProduct } from '../types';
// import { User } from '@/types/users';

type Data = {
    message: string;
    categories: TCategory[];
    count: number;
    total_categories: number;
};

type ApiResponse = {
    status: string;
    status_code: number;
    data: Data;
    errors: null | string;
};



export const getAllCategories = async (company: string): Promise<ApiResponse> => {
    const response = await managementAxios.get(`/api/v1/stock/categories/?company=${company}`);
    return response.data
};



export const UseGetCategories = (company: string) => {
    return useQuery({
        queryKey: ['categories-list', company],
        queryFn: () => getAllCategories(company),
    });
};
export const getAllSubCategories = async (company: string): Promise<ISubcategory> => {
    const response = await managementAxios.get(`/api/v1/stock/subcategories/?company=${company}`);
    return response.data
};



export const UseGetSubCategories = (company: string) => {
    return useQuery({
        queryKey: ['sub-categories-list', company],
        queryFn: () => getAllSubCategories(company),
    });
};
