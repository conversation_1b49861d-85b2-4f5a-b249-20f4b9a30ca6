'use client'
import { AxiosError } from 'axios';
import { managementAxios } from '@/lib/axios';
import { useMutation, useQuery } from '@tanstack/react-query';

export type MerchantAccountDetailDTO = {
    branch: string;
    company: string;
    account_name: string;
    account_number: string;
    bank_name: string;
    bank_code: string;
};

type ApiResponse = {
    status: string;
    status_code: number;
    data: {
        message: string;
    };

};

export interface GetMerchantAccountDetailsResponse {
    status: string
    status_code: number
    data: Data
    errors: any
}

export interface Data {
    message: string
    data: SingleMerchantAccountDetailDTO[]
    count: number
}

export interface SingleMerchantAccountDetailDTO {
    id: string
    created_at: string
    updated_at: string
    account_name: string
    account_number: string
    bank_name: string
    bank_code: string
    company: string
    branch: string
}


const collectMerchantAccountDetail = async (data: MerchantAccountDetailDTO): Promise<ApiResponse> => {
    const response = await managementAxios.post<ApiResponse>(
        `/api/v1/sales/collect_merchant_account_detail/`,
        data
    );
    return response.data;
};

const getMerchantAccountDetails = async ({ company, branch }: { company: string, branch: string }): Promise<GetMerchantAccountDetailsResponse> => {
    const response = await managementAxios.get<GetMerchantAccountDetailsResponse>(
        `/api/v1/sales/collect_merchant_account_detail/?company=${company}&branch=${branch}`
    );
    return response.data;
};

export const useCollectMerchantAccountDetail = () => {
    return useMutation<ApiResponse, AxiosError<ApiResponse>, MerchantAccountDetailDTO>({
        mutationFn: collectMerchantAccountDetail,
    });
};

export const useGetMerchantAccountDetails = ({ company, branch }: { company: string, branch: string }) => {
    return useQuery({
        queryKey: ['merchant-account-details', company, branch],
        queryFn: () => getMerchantAccountDetails({ company, branch }),
        enabled: !!company && !!branch,
    });
}; 