"use client";
import { managementAxios } from "@/lib/axios";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { AxiosError } from "axios";

// import { useRouter } from "next/navigation";

export interface TSalesConfirmationResult {
  message: string;
  company_name: string;
  branch_name: string;
  branch_address: string;
  branch_phone?: string;
  updated_at: string;
  invoice_id: string;
  charges: number;
  batch_id: string;
  sub_total: number;
  discount_value: number;
  delivery_fee: number;
  vat: number;
  total: number;
  items: Item[];
  means_of_payment: string;
  customer: Customer;
}

export interface RegisterSalesResult {
  status: string;
  status_code: number;
  data: RegisterSalesData;
  errors: any;
}

export interface RegisterSalesData {
  invoice_id: string;
  batch_id: string;
  status: string;
  sub_total: number;
  charges: number;
  discount_value: number;
  delivery_fee: number;
  vat: number;
  total: number;
  items: RegisterSalesItem[];
}

export interface RegisterSalesItem {
  category: string;
  item_description: string;
  unit_price: number;
  quantity: number;
}

export interface Item {
  category_id: string;
  item_id: string;
  quantity: number;
  amount: string;
  item_description: string;
}

export interface Customer {
  name: string;
  phone: string;
  email: string;
  address: string;
}

type ApiResponse = {
  status: string;
  status_code: number;
  data: TSalesConfirmationResult;
  errors: any;
};

type saveSaleFormData = {
  company: string;
  branch: string;
  batch_id: string;
  means_of_payment: string;
  customer?: string;
};

export const loginApi = {
  registerSale: async (data: saveSaleFormData): Promise<ApiResponse> => {
    const response = await managementAxios.patch<ApiResponse>(
      `/api/v1/sales/confirm_sales/`,
      data
    );
    return response.data;
  },
};

export const UseConfirmSale = () => {
  const queryClient = useQueryClient();
  return useMutation<ApiResponse, AxiosError<ApiResponse>, saveSaleFormData>({
    mutationFn: loginApi.registerSale,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ["products-list"] });
    },
  });
};
