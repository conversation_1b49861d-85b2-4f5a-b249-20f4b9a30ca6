import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { managementAxios } from "@/lib/axios";
import { TProduct } from "../types";
// import { User } from '@/types/users';

export type TFetchProductsOptions = {
  company: string;
  category: string | null | undefined;
  subcategory: string | null | undefined;
  search?: string;
  branch: string;
  pageIndex: number
  pageSize: number
  price_tag?: string
};

export interface RootResponse {
  status: string;
  status_code: number;
  data: Data;
  nextPageIndex?: number
}

export interface Data {
  message: string;
  stock: TProduct[];
  count: number;
  total_stock: number;
}

export interface IPriceTagResponse {
  status: string;
  status_code: number;
  data: Data;
}

export interface IPriceTagData {
  message: string;
  count: number;
  price_tags: IPriceTags;
}
interface IPriceTags {
  id: string;
  created_at: string;
  updated_at: string;
  system_default: boolean;
  name: string;
  company: string;
  created_by: string;
  updated_by: string;
}

export const getAllProducts = async (
  fetchOptions: TFetchProductsOptions
): Promise<RootResponse> => {
  const { branch, company, category, search, pageIndex, pageSize, price_tag, subcategory} = fetchOptions;
  const categoryExistsQueryParams =
    !!category || category == "all" ? `&category_id=${category}` : "";
  const subcategoryExistsQueryParams =
    !!subcategory || subcategory == "all" ? `&subcategory_id=${subcategory}` : "";

  const searchQueryParamsExists = !!search ? `&search=${search}` : "";
  const branchParams = branch ? `&branch=${branch}` : "";
  const response = await managementAxios.get(
    `/api/v1/stock/branch/available_stock?company=${company}${branchParams}${searchQueryParamsExists}${categoryExistsQueryParams}${subcategoryExistsQueryParams}&page=${pageIndex}&size=${pageSize}&price_tag=${price_tag}`
  );

  return response.data;
};

export const UseGetProducts = (fetchOptions: TFetchProductsOptions) => {
  return useInfiniteQuery({
    queryKey: ["products-list", fetchOptions],
    queryFn: ({ pageParam = 1 }) => getAllProducts({...fetchOptions, pageIndex: pageParam,}),
    getNextPageParam: (lastPage, pages) => {
      const totalItems = lastPage?.data.total_stock;
      const currentItemCount = pages.reduce((acc, page) => acc + page.data.stock.length, 0);

      if (currentItemCount >= totalItems) {
          return undefined;
      }

      return pages.length + 1;
  },
    // getNextPageParam: (lastPage) => (lastPage as RootResponse).nextPageIndex,
    initialPageParam: 1
  });
};





type FetchOptions = {
  companyId: string;
};






export const getPriceTags = async (
  fetchOptions: FetchOptions
): Promise<IPriceTagData> => {
  const { companyId } = fetchOptions;

  const response = await managementAxios.get(
    `/api/v1/stock/price_tags/?company=${companyId}`
  );
  return response.data.data;
};

export const useGetPriceTags = (
  fetchOptions: FetchOptions,
) => {
  return useQuery({
    queryKey: ["price-tags-list", fetchOptions],
    queryFn: () => getPriceTags(fetchOptions),
    enabled: !!fetchOptions,
  });
};
