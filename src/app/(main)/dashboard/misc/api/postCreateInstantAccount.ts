'use client'
import { AxiosError } from 'axios';

import { managementAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';



type ApiResponse = {
    status: string;
    status_code: number;
    data: {
        bank_name: string;
        bank_code: string;
        account_name: string;
        account_number: string;
    };
    errors: any;
};


type instantAccountDTO = {
    company: string,
    branch: string,
    batch_id: string,
}


const getInstantAccount = async (data: instantAccountDTO): Promise<ApiResponse> => {
    const response = await managementAxios.post<ApiResponse>(`/api/v1/sales/branch_instant_account/`, data);
    return response.data;
}



export const UseGetInstantAccount = () => {
    return useMutation<ApiResponse, AxiosError<ApiResponse>, instantAccountDTO>({
        mutationFn: getInstantAccount,

    });
};