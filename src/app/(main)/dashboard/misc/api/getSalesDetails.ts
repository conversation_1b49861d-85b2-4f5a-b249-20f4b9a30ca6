import { useQuery } from '@tanstack/react-query';

import { managementAxios } from "@/lib/axios";
import { SalesTransactionDto } from './getPendingOrders';
import { InvoicesTransactionDetailsDto } from '@/app/(main)/transactions/misc/types';

export interface InvoiceItemsDtoResponse {
  status: string;
  status_code: number;
  data: {
    message: string;
    transaction_items: Array<InvoiceItemsDto>;
    transaction_details: InvoicesTransactionDetailsDto;

    count: number;
  };
  errors: null;
}

export type InvoiceItemsDto = {
  cashier: string;
  created_at: string; // ISO Date string
  category_id: string;
  category: string;
  item_id: string;
  item_description: string;
  batch_id: string;
  status: string;
  payment_method: string;
  amount: number;
  quantity: number;
  discount: number;
  vat: number;
  delivery_fee: number;
  sub_total: number;
  total: number;
  reference: string;
  customer?: string;
}


interface FetchTeamsOptions {
  companyId: string;
  branchId: string;
  referenceId: string;
  pageIndex: number;
  pageSize: number;
  startDate?: string | undefined;
  endDate?: string | undefined;
  search?: string;
}

export const getInvoiceItems = async ({
  companyId,
  branchId,
  referenceId,
  pageIndex,
  pageSize,
  // startDate,
  // endDate,
  // search,
}: FetchTeamsOptions): Promise<InvoiceItemsDtoResponse> => {
  const response = await managementAxios.get(
    `/api/v1/sales/transaction/items?company=${companyId}&branch=${branchId}&page=${pageIndex}&size=${pageSize}&search=${referenceId}`
  );

  return response.data; //as StockTableResponse;
};

export const useGetInvoiceItems = (fetchOptions: FetchTeamsOptions) => {
  return useQuery({
    queryKey: ['get-invoice-items', fetchOptions],
    queryFn: () => {
      if (fetchOptions) return getInvoiceItems(fetchOptions);
    },
    enabled: !!fetchOptions.referenceId,
  });
};
