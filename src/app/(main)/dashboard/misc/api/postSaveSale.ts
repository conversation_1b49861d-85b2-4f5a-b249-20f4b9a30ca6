"use client";
import { managementAxios } from "@/lib/axios";

import { useMutation } from "@tanstack/react-query";

import { AxiosError } from "axios";

import { useRouter } from "next/navigation";

import { TCartItem } from "../types";

type ApiResponse = {
  status: string;
  status_code: number;
  data: {
    invoice_id: string;
    batch_id: string;
    sub_total: number;
    discount_value: number;
    delivery_fee: number;
    vat: number;
    total: number;
    items: Array<{
      item_description: string;
      unit_price: number;
      quantity: number;
    }>;
  };
  errors: any;
};

type saveSaleFormData = {
  company: string;
  branch: string;
  sales_tag?: string;
  cart: TCartItem[];
  discount_value?: number | string;
  delivery_fee?: number | string;
  save?: boolean;
  batch_id?: string;
};

type TdataToSubmit = {
  company: string;
  branch: string;
  sales: {
    item_id: string;
    quantity: number;
    amount: string | number;
    category_id: string | undefined;
    item_discount_value: number;
  }[];
  batch_id: string | undefined;
  device: string;
  save?: boolean,
  sales_tag?: string;
}

export const loginApi = {
  registerSale: async (data: saveSaleFormData): Promise<ApiResponse> => {
    const sales = data.cart.map((item) => {
      return {
        item_id: item.item_id,
        quantity: item.quantity,
        amount: parseInt(item.selling_price),
        category_id: item.category_id,
        item_discount_value: item.item_discount_value || 0,
      };
    });
    const dataToSubmit = {
      company: data.company,
      branch: data.branch,
      sales,
      device: "WEB_POS",
      discount_value: data.discount_value,
      save: data.save,
      sales_tag: data.sales_tag ?? null
    };

    const response = await managementAxios.post<ApiResponse>(
      `/api/v1/sales/register/`,
      dataToSubmit
    );
    return response.data;
  },
};

export const UseSaveSale = () => {
  const router = useRouter();

  return useMutation<ApiResponse, AxiosError<ApiResponse>, saveSaleFormData>({
    mutationFn: loginApi.registerSale,
  });
};

export const updateRegisteredSale = async (
  data: saveSaleFormData
): Promise<ApiResponse> => {
  const sales = data.cart.map((item) => {
    return {
      item_id: item.item_id,
      quantity: item.quantity,
      amount: item?.selling_price,
      category_id: item.category_id,
      item_discount_value: item.item_discount_value || 0,
    };
  });
  const dataToSubmit: TdataToSubmit = {
    company: data.company,
    branch: data.branch,
    sales,
    batch_id: data.batch_id,
    device: "WEB_POS",
  };
  if (data.save) {
    dataToSubmit.save = true;
  }
  if (data.sales_tag) {
    dataToSubmit.sales_tag = data.sales_tag;
  }
  const response = await managementAxios.patch<ApiResponse>(
    `/api/v1/sales/register/`,
    dataToSubmit
  );
  return response.data;
};

export const UseUpdateSavedSale = () => {
  return useMutation<ApiResponse, AxiosError<ApiResponse>, saveSaleFormData>({
    mutationFn: updateRegisteredSale,
  });
};
