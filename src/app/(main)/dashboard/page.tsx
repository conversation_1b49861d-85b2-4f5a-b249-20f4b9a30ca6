// SalesDashboard.tsx

"use client";

import { Eye, LockIcon } from "lucide-react";
import React, { type KeyboardEvent, useEffect, useRef, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";

import { LogoutIcon, Paybox360, ScanIcon } from "@/components/icons";
import { <PERSON><PERSON>, LinkButton } from "@/components/ui";
import { useCartStore, useCompanyStore } from "@/stores";
import { convertToTitleCase } from "@/utils/strings";
import type { PaginationState } from "@tanstack/react-table";
import Link from "next/link";
import {
  UseGetCategories,
  UseGetSubCategories,
  UseGetProducts,
} from "./misc/api";
import {
  Cart,
  CategoriesList,
  Checkout,
  ProductContainer,
  SearchBox,
  Summary,
} from "./misc/components";
import type { TProduct } from "./misc/types";
import { type Itag, PriceTag } from "./misc/components/Pricetag";
import { useGetPriceTags } from "./misc/api/getProducts";
import { useDebounce } from "@/hooks";
import useInfiniteScroll from "react-infinite-scroll-hook";
import { ReceiptItem, Refresh } from "iconsax-react";
import { useRouter } from "next/navigation";
import SubCategoriesList from "./misc/components/ProductSubCategoriesList";
import { useBarcodeScanner } from "@/hooks/useBarcodeScanner";

const SalesDashboard: React.FC = () => {
  const [isHardRefresh, setIsHardRefresh] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [sales_tag, setSales_tag] = useState<string | null>(null);
  const [selectedSubCategory, setSelectedsubCategory] = useState<string | null>(
    null
  );
  const [discountValue, setDiscountValue] = useState(0);
  const [overallPrice, setOverallPrice] = useState(0);
  const [selectedPriceTag, setSelectedPriceTag] = useState<Itag>({
    name: "Selling Price",
    id: "",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [isMobileView, setIsMobileView] = useState(false);
  // const [showBottomSheet, setShowBottomSheet] = useState(false)
  const [showCheckoutSheet, setShowCheckoutSheet] = useState(false);
  const [showMenuSheet, setShowMenuSheet] = useState(false);

  useEffect(() => {
    const updateSize = () => setIsMobileView(window.innerWidth < 768);
    updateSize();
    window.addEventListener("resize", updateSize);
    return () => window.removeEventListener("resize", updateSize);
  }, []);

  const searchQuery = useDebounce(searchTerm, 500);
  const { branch, company, first_name, last_name, branchData } =
    useCompanyStore();

  const fetchOptions = {
    company,
    branch,
    category: selectedCategory,
    subcategory: selectedSubCategory,
    search: searchQuery,
    pageIndex: 1,
    pageSize: 100,
    price_tag:
      selectedPriceTag.name === "Selling Price" ? "" : selectedPriceTag.id,
  };

  const { data: priceTags } = useGetPriceTags({ companyId: company });
  const {
    data,
    isLoading: isFetchingProducts,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = UseGetProducts(fetchOptions);

  const [sentryRef] = useInfiniteScroll({
    loading: isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    onLoadMore: fetchNextPage,
    disabled: !!error,
    rootMargin: "0px 0px 800px 0px",
  });

  const searchRef = useRef<HTMLInputElement>(null);
  const cartRef = useRef<HTMLDivElement>(null);
  const summaryRef = useRef<HTMLDivElement>(null);
  const checkoutRef = useRef<HTMLDivElement>(null);
  const productsContainerRef = useRef<HTMLUListElement>(null);
  const categoriesListRef = useRef<HTMLDivElement>(null);
  const subcategoriesListRef = useRef<HTMLDivElement>(null);

  const router = useRouter();
  const {
    active_cart,
    update_item_quantity,
    delete_item,
    add_item,
    remove_item,
    clear_cart,
  } = useCartStore();
  const [paymentMethod, setPaymentMethod] = useState("CASH");

  const available_stock = data?.pages.flatMap((page) => page?.data?.stock);

  useBarcodeScanner({
    products: available_stock,
    onProductFound: add_item,
  });

  useEffect(() => {
    if (branch) {
      active_cart.map((v) => {
        if (v.branch !== branch) clear_cart();
      });
    }
  }, [branch]);

  useEffect(() => {
    if (isHardRefresh) clear_cart();
  }, [isHardRefresh]);

  const todaysDate = `${new Date().getDate().toString().padStart(2, "0")}/${(
    new Date().getMonth() + 1
  )
    .toString()
    .padStart(2, "0")}/${new Date().getFullYear()}`;

  const { data: categories, isLoading: isFetchingCategories } =
    UseGetCategories(company);
  const { data: subcategories, isLoading: isFetchingSubCategories } =
    UseGetSubCategories(company);

  const handleCategorySelect = (category: { name: string; id: string }) => {
    setSelectedCategory(category.id);
    setProductsToDisplay(
      available_stock?.filter((item) => item.category_id === category.id)
    );
  };

  const handleSubCategorySelect = (subcategory: {
    name: string;
    id: string;
  }) => {
    setSelectedsubCategory(subcategory.id);
    setProductsToDisplay(
      available_stock?.filter((item) => item.subcategory_id === subcategory.id)
    );
  };

  const [{ pageIndex, pageSize }, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [productsToDisplay, setProductsToDisplay] = useState<
    TProduct[] | undefined
  >(available_stock);

  useEffect(() => {
    if (!isFetchingProducts && data) setProductsToDisplay(available_stock);
  }, [isFetchingProducts, data]);

  return (
    <div className="lg:grid lg:grid-rows-[max-content,1fr] flex flex-col w-screen h-screen overflow-hidden">
      <header className="flex items-center justify-between gap-10 p-4 h-[8.5vh]">
        <Paybox360 />

        {isMobileView ? (
          <div className="flex gap-2 items-center">
            <Button
              variant="secondary"
              size="icon"
              className="p-[10px] size-full rounded-full"
              onClick={() => {
                setShowCheckoutSheet(true);
                setShowMenuSheet(false);
              }}
            >
              <ReceiptItem size={14} />
            </Button>
            <Button
              variant="secondary"
              className="px-2 h-8 text-sm rounded-lg"
              onClick={() => {
                setShowMenuSheet(true);
                setShowCheckoutSheet(false);
              }}
            >
              Menu
            </Button>
          </div>
        ) : (
          <>
            <div className="bg-[#1E1E1E] p-1.5 rounded-lg max-h-14">
              <p className="flex flex-col p-1 px-4 rounded-lg bg-[#D9D9D91A] text-white">
                <span className="text-[0.9rem] font-medium">
                  {branchData.company}
                </span>
                <span className="text-xs text-white/70">{branchData.name}</span>
              </p>
            </div>

            <SearchBox
              setSearchTerm={setSearchTerm}
              ref={searchRef}
              products={available_stock!}
              isFetchingProducts={isFetchingProducts}
              onAddToCart={add_item}
              onRemoveItem={remove_item}
              cartItems={active_cart}
            />

            <div className="flex items-center gap-4">
              <PriceTag
                tags={
                  (priceTags?.price_tags as any)?.concat([
                    { name: "Selling Price" },
                  ]) ?? []
                }
                value={selectedPriceTag}
                onValueChange={(e) => setSelectedPriceTag(e)}
              />
              <LinkButton
                href="/history"
                variant="secondary"
                size="thin"
                className="flex items-center justify-between gap-2"
              >
                <span>Sales record </span> <Eye size={20} />
              </LinkButton>
              <LinkButton
                href="/transactions"
                variant="secondary"
                className="flex items-center w-12"
                size="icon"
              >
                <ScanIcon />
              </LinkButton>
              <LinkButton
                href="/orders"
                variant="secondary"
                size="thin"
                className="flex items-center justify-between gap-2"
              >
                <span>Orders</span> <Eye size={20} />
              </LinkButton>
              <LinkButton
                href="/onboard/break"
                variant="secondary"
                className="flex items-center w-12"
                size="icon"
              >
                <LockIcon />
              </LinkButton>
              <Button
                variant="unstyled"
                className="flex items-center gap-2 py-0"
              >
                <div className="flex flex-col items-start gap-0">
                  <p className="text-[0.85rem]">{`${convertToTitleCase(
                    first_name
                  )} ${convertToTitleCase(last_name)}`}</p>
                  <span className="text-muted-foreground text-[0.7rem]">
                    {todaysDate}
                  </span>
                </div>
                <Link
                  className="rounded-lg p-1 bg-[#3a1720ba]"
                  href="/onboard/shift"
                >
                  <LogoutIcon width={20} height={20} />
                </Link>
              </Button>
            </div>
          </>
        )}
      </header>

      <main className="w-full flex grow md:grid md:grid-cols-[1fr,0.45fr] 2xl:grid-cols-[1fr,500px] h-[91.5vh]">
        <div className="overflow-y-scroll">
          <CategoriesList
            categories={categories?.data.categories || []}
            isLoading={isFetchingCategories}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
            ref={categoriesListRef}
          />
          <SubCategoriesList
            categories={subcategories?.data.subcategories || []}
            isLoading={isFetchingSubCategories}
            selectedCategory={selectedSubCategory}
            onCategorySelect={handleSubCategorySelect}
            ref={subcategoriesListRef}
          />
          <ProductContainer
            products={productsToDisplay || []}
            isFetchingProducts={isFetchingProducts}
            onAddToCart={add_item}
            onRemoveFromCart={remove_item}
            cartItems={active_cart}
            ref={productsContainerRef}
            branch={branch}
            company={company}
            loadMoreDataObserver={<div ref={sentryRef} />}
          />
        </div>

        {!isMobileView && (
          <aside className="relative flex flex-col gap-2.5 pl-1 pr-5 pt-0 pb-4 overflow-y-scroll h-[91.5vh]">
            <Button
              onClick={() => setIsHardRefresh(true)}
              className="bg-[#1C1C1C] text-white flex justify-between"
            >
              <span>Refresh</span>
              <Refresh
                className={isHardRefresh ? "animate animate-spin" : ""}
              />
            </Button>
            <div className="flex flex-col gap-2.5 grow overflow-y-scroll">
              <Cart
                items={active_cart}
                onUpdateQuantity={update_item_quantity}
                onRemoveItem={delete_item}
                ref={cartRef}
                available_stock={available_stock!}
              />
              <Summary
                cart={active_cart}
                ref={summaryRef}
                paymentMethod={paymentMethod}
                setOverallPrice={setOverallPrice}
                discountValue={discountValue}
                setDiscountValue={setDiscountValue}
                setSales_tag={setSales_tag}
                sales_tag={sales_tag ?? ""}
              />
            </div>
            <Checkout
              sales_tag={sales_tag ?? ""}
              discountValue={discountValue}
              setDiscountValue={setDiscountValue}
              ref={checkoutRef}
              paymentMethod={paymentMethod}
              setPaymentMethod={setPaymentMethod}
              overallPrice={overallPrice}
            />
          </aside>
        )}

        {/* Checkout Sheet */}
        <AnimatePresence>
          {isMobileView && showCheckoutSheet && (
            <motion.div
              initial={{ y: "100%" }}
              animate={{ y: 0 }}
              exit={{ y: "100%" }}
              transition={{ duration: 0.3 }}
              className="fixed overflow-y-auto bottom-0 left-0 right-0 z-50 h-[95vh] bg-[#121212] shadow-lg rounded-t-2xl"
            >
              <div className="flex justify-between items-center px-4 py-2 border-b border-white/10">
                <h2 className="text-white">Checkout</h2>
                <button
                  onClick={() => setShowCheckoutSheet(false)}
                  className="text-white"
                >
                  ✕
                </button>
              </div>
              <div className="p-4 overflow-y-auto">
                <Cart
                  items={active_cart}
                  onUpdateQuantity={update_item_quantity}
                  onRemoveItem={delete_item}
                  available_stock={available_stock!}
                />
                <Summary
                  cart={active_cart}
                  paymentMethod="CASH"
                  setOverallPrice={setOverallPrice}
                  discountValue={discountValue}
                  setDiscountValue={setDiscountValue}
                  setSales_tag={setSales_tag}
                  sales_tag={sales_tag ?? ""}
                />
                <Checkout
                  sales_tag={sales_tag ?? ""}
                  discountValue={discountValue}
                  setDiscountValue={setDiscountValue}
                  paymentMethod="CASH"
                  setPaymentMethod={() => {}}
                  overallPrice={overallPrice}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Menu Sheet */}
        <AnimatePresence>
          {isMobileView && showMenuSheet && (
            <motion.div
              initial={{ y: "100%" }}
              animate={{ y: 0 }}
              exit={{ y: "100%" }}
              transition={{ duration: 0.3 }}
              className="fixed overflow-y-auto bottom-0 left-0 right-0 z-50 h-[70vh] bg-[#1A1A1A] shadow-lg rounded-t-2xl"
            >
              <div className="flex justify-between items-center px-4 py-2 border-b border-white/10">
                <h2 className="text-white">Menu</h2>
                <button
                  onClick={() => setShowMenuSheet(false)}
                  className="text-white"
                >
                  ✕
                </button>
              </div>
              <div className="p-4 space-y-4 overflow-y-auto">
                <SearchBox
                  setSearchTerm={setSearchTerm}
                  ref={searchRef}
                  products={available_stock!}
                  isFetchingProducts={isFetchingProducts}
                  onAddToCart={add_item}
                  onRemoveItem={remove_item}
                  cartItems={active_cart}
                />
                <PriceTag
                  tags={(priceTags?.price_tags || ([] as any)).concat([
                    { name: "Selling Price" },
                  ])}
                  value={selectedPriceTag}
                  onValueChange={setSelectedPriceTag}
                  className="!w-full"
                />
                <LinkButton
                  href="/history"
                  variant="secondary"
                  size="thin"
                  className="flex items-center gap-2"
                >
                  Sales record <Eye size={20} />
                </LinkButton>
                <LinkButton
                  href="/orders"
                  variant="secondary"
                  size="thin"
                  className="flex items-center gap-2"
                >
                  Orders <Eye size={20} />
                </LinkButton>
                <div className="flex items-center justify-between">
                  <LinkButton
                    href="/onboard/break"
                    variant="secondary"
                    className="flex items-center w-12"
                    size="icon"
                  >
                    <LockIcon />
                  </LinkButton>
                  <Button
                    variant="unstyled"
                    className="flex items-center gap-2 py-0"
                  >
                    <div className="flex flex-col items-start">
                      <p className="text-[0.85rem]">{`${convertToTitleCase(
                        first_name
                      )} ${convertToTitleCase(last_name)}`}</p>
                      <span className="text-muted-foreground text-[0.7rem]">
                        {todaysDate}
                      </span>
                    </div>
                    <Link
                      className="rounded-lg p-1 bg-[#3a1720ba]"
                      href="/onboard/shift"
                    >
                      <LogoutIcon width={20} height={20} />
                    </Link>
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </main>
    </div>
  );
};

export default SalesDashboard;
