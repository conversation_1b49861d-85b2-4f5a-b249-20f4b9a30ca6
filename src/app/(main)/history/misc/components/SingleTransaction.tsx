"use client";

import * as React from "react";

import {
  Button,
  <PERSON>alog,
  DialogBody,
  DialogClose,
  DialogContent,
  // DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui";

// import { useGetInvoiceItems } from '../../api';
import {
  ISalesTransactionDto,
  SalesTransactionDto,
} from "@/app/(main)/dashboard/misc/api/getPendingOrders";
import {
  InvoiceItemsDto,
  useGetInvoiceItems,
} from "@/app/(main)/dashboard/misc/api/getSalesDetails";
import { useCartStore, useCompanyStore } from "@/stores";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

// import { useRouter } from 'next/navigation';
interface SingleTxProps {
  singleInvoice: ISalesTransactionDto;
  isSingleTxOpen: boolean;
  closeSingleTxOpen: () => void;
}

function SingleTransaction({
  isSingleTxOpen,
  closeSingleTxOpen,
  singleInvoice,
}: SingleTxProps) {
  const router = useRouter();
  const { company, branch } = useCompanyStore();
  const {
    // active_cart,
    // update_item_quantity,
    // delete_item,
    update_item_discount,
    add_item_from_saved_sales,
    clear_cart,
  } = useCartStore();

  const fetchOptions = {
    companyId: company,
    branchId: branch,
    referenceId: singleInvoice?.batch_id as string,
    pageIndex: 1,
    pageSize: 100,
  };
  const { data } = useGetInvoiceItems(fetchOptions);

  const handleOnOpenChange = (open: boolean) => {
    if (!open) {
      closeSingleTxOpen();
      // router.back();
    }
  };
  const handleSaveSalesToStore = () => {
    clear_cart();
    (singleInvoice.sales ?? []).map((v) => {
      // Calculate item discount value - if the backend stores it as item_discount_value, use that
      // Otherwise, calculate it from the discount field (assuming it's per-item discount)
      const itemDiscountValue = (v as any).item_discount_value || (v.discount || 0);

      add_item_from_saved_sales({
        category: v.item_description,
        category_id: v.category_id,
        item: v.item_description,
        item_id: v.item_id,
        quantity: v.quantity,
        stock_price: `${v.amount / v.quantity}`,
        selling_price: `${v.amount / v.quantity}`,
        image: null,
        branch: branch,
      });

      // Update the discount value after adding the item
      if (itemDiscountValue > 0) {
        update_item_discount(v.item_id, itemDiscountValue);
      }
    });
    toast.success("Cart updated with saved sale");
    router.push(`/dashboard?batch_id=${singleInvoice.batch_id}&sales_tag=${singleInvoice.sales_tag}`);
  };

  return (
    <div>
      <Dialog open={isSingleTxOpen} onOpenChange={handleOnOpenChange}>
        <DialogTrigger className="hidden px-[.875rem] sm:py-2.5">
          Transaction
        </DialogTrigger>

        <DialogContent>
          <section className="flex justify-between">
            <div>Transaction</div>
            <DialogClose>Close</DialogClose>
          </section>

          <DialogBody className="!p-0">
            <div className="border rounded-lg grid divide-x space-x-2 my-6">
              <div className="p-3">
                <p className="text-sm">{singleInvoice?.invoice ?? ""}</p>
                <p className="text-xs text-gray-400">Invoice</p>
              </div>
            </div>
            <div className="border rounded-lg flex justify-evenly  space-x-2 p-2 my-6">
              {data?.data?.transaction_details?.payment_details?.map(
                (v, idx) => (
                  <div key={idx}>
                    <div>
                      <div className="font-semibold">₦{v?.amount.toLocaleString()}</div>
                      <div className="text-gray-400 text-xs">{v?.method}</div>
                    </div>
                  </div>
                )
              )}
            </div>

            <div className="p-2 border-b rounded-lg grid grid-cols-3 items-center text-[14px] font-medium">
              <div>Name</div>
              <div className="text-center">Qty</div>
              <div className="text-right">Amount</div>
            </div>

            <div className="divide-y-2 rounded-lg space-y-1 ">
              {(singleInvoice ?? [])?.sales?.map((v) => (
                <div
                  className="p-2  grid grid-cols-3 items-center text-[14px] border-t"
                  key={v.item_id}
                >
                  <div>{v?.item_description ?? ""}</div>
                  <div className="text-center">{v?.quantity ?? ""}</div>
                  <div className="text-right">₦{v?.amount.toLocaleString() ?? ""}</div>
                </div>
              ))}
            </div>

            <div className="border rounded-lg p-3 flex justify-between items-center space-x-2 my-6">
              <p className="text-gray-400">Total</p>
              <p className="text-sm">
                ₦{singleInvoice?.total_sales_amount ?? ""}
              </p>
            </div>

            <div className="flex justify-end">
              <Button onClick={handleSaveSalesToStore} className="my-4ß">
                Checkout
              </Button>
            </div>
          </DialogBody>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default SingleTransaction;
