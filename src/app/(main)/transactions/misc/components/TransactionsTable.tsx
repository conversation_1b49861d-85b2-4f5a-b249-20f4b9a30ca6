'use client'

import React, { useEffect, useState } from 'react'
import { format } from 'date-fns'

import { faker } from '@faker-js/faker';
import { Checkbox, Input, DataTable } from '@/components/ui'
import { ColumnDef, PaginationState } from '@tanstack/react-table'
import { addCommasToNumber } from '@/utils/numbers'
import { UseGetTransactions } from '../api';
import { useCompanyStore } from '@/stores';
import { SalesTransaction, TransactionsResponseData } from '../types';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import { convertNumberToNaira } from '@/utils/currency';

const TransactionsTable = () => {
    const { company, branch } = useCompanyStore()
    const [search, setSearch] = useState('')
    const [{ pageIndex, pageSize }, setPagination] =
        React.useState<PaginationState>({
            pageIndex: 0,
            pageSize: 10,
        });

    const fetchOptions = {
        company,
        branch,
        search,
        pageSize: pageSize,
        pageIndex: pageIndex,
    }

    const { data, isLoading, isFetching, refetch } = UseGetTransactions(fetchOptions)

    const totalItems = data?.data.total_transactions || 1;
    const pageCount = React.useMemo(() => {
        if (isLoading) return 1;
        else if (!isLoading && data) {
            const totalTransactions = data?.data?.total_transactions;
            return totalTransactions > 0 ? Math.ceil(totalTransactions / pageSize) : 1;
        }
        else return 1
    }, [pageSize, isLoading, data]);

    useEffect(() => {
        refetch()
    }, [pageIndex, refetch])




    const columns: ColumnDef<SalesTransaction>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && "indeterminate")
                    }
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label="Select row"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            id: 's/n',
            header: 'S/N',
            cell: ({ row }) => row.index + 1,
        },
        {
            id: 'id',
            header: 'Transaction ID',
            accessorKey: 'id',
        },
        {
            id: 'means_of_payment',
            header: 'Transaction type',
            accessorKey: 'means_of_payment',
        },
        {
            id: 'amount_paid',
            header: 'Amount',
            accessorKey: 'amount_paid',
            cell: ({ row }) => convertNumberToNaira(parseInt(row.original.amount_paid)),
        },
        {
            id: 'created_at',
            header: 'Date',
            cell: ({ row }) => format(row.original.created_at, 'MMM dd, yyyy HH:MMaa'),
        },
        {
            id: 'status',
            header: 'Status',
            accessorKey: 'status',
            cell: ({ row }) => convertKebabAndSnakeToTitleCase(row.original.status),
        },
    ];


    return (
        <div>
            <header>
                <div className='sticky top-0 flex items-center justify-between w-full pl-4 py-4 bg-[#1C1C1C] z-[2]'>
                    <h2 className='font-medium text-xl'>Transactions</h2>
                    <Input placeholder='Search' className='bg-transparent h-10 text-xs rounded-lg min-w-lg'
                        value={search} onChange={(e) => setSearch(e.target.value)}
                    />
                </div>
            </header>
            <DataTable
                isLoading={isLoading}
                isFetching={isFetching}
                pageIndex={pageIndex}
                pageSize={pageSize}
                pageCount={pageCount}
                totalItems={totalItems}
                columns={columns}
                data={data?.data.sales_transactions || []}
                onPaginationChange={setPagination}
            />
        </div>
    );
};

export default TransactionsTable;