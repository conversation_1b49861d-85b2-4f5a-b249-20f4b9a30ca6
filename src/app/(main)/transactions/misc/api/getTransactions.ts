import { useQuery } from '@tanstack/react-query';

import { managementAxios } from '@/lib/axios';
import { useCompanyStore } from '@/stores';
import { TransactionsResponseData } from '../types';




interface fetchOptions {
    company: string;
    branch: string;
    pageSize: number;
    pageIndex: number;
    search?: string;

}
export const getTransactions = async (data: fetchOptions): Promise<TransactionsResponseData> => {
    const { company, branch, pageSize, pageIndex } = data
    const response = await managementAxios.get(`/api/v1/sales/register/?company=${company}&branch=${branch}&page=${pageIndex + 1}&size=${pageSize}&search=${data.search}&device=WEB_POS`);
    return response.data
};



export const UseGetTransactions = (data: fetchOptions) => {
    return useQuery({
        queryKey: ['companies-transactions', data.company, data.branch, data.search, data.pageIndex, data.pageSize],
        queryFn: () => getTransactions(data),
    });
};
