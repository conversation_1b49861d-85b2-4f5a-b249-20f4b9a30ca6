export type SalesTransaction = {
    id: string;
    created_at: string;
    updated_at: string;
    batch_id: string;
    status: "PENDING" | "COMPLETED" | "CANCELLED";
    means_of_payment: string | null;
    total_sales_amount: string;
    discount_value: string;
    delivery_fee: string;
    vat_amount: string;
    total_cost: string;
    sales_tag?: string;
    amount_paid: string;
    paid: boolean;
    paid_at: string | null;
    device: string;
    company: string;
    branch: string;
    customer: string;
    invoice: string;
    created_by: string;
    updated_by: string | null;
};

export type TransactionsResponseData = {
    status: "success";
    status_code: number;
    data: {
        message: string;
        total_sales: number;
        total_transactions: number;
        count: number;
        sales_transactions: SalesTransaction[];
    };

}

export interface InvoiceItemsDtoResponse {
    status: string;
    status_code: number;
    data: {
      message: string;
      transaction_items: Array<InvoiceItemsDto>;
      transaction_details: InvoicesTransactionDetailsDto;
      count: number;
    };
    errors: null;
  }
  
  export type InvoiceItemsDto = {
    created_at: string;
    item_description: string;
    amount: string;
    quantity: number;
    reference: string;
    item_id: string;
    category_id?: string;
    sales_tag?: string;
  };
  export type InvoicesTransactionDetailsDto = {
    total_amount: number,
    attendant: string,
    invoice: string,
    payment: {
      method: string,
      amount: number
    },
    payment_details: [
      {
        method: string,
        amount: number
      },
      {
        method: string,
        amount: number
      }
    ]
  };