'use client'

import React, { KeyboardEvent } from 'react';
import { TransactionsTable } from './misc/components';
import AppHeader from '@/components/layout/BaseAppHeader';
import { useCompanyStore } from '@/stores';
import { convertToTitleCase } from '@/utils/strings';


const HistoryPage = () => {
    const { company, branch, first_name, last_name } = useCompanyStore()
    return (
        <div className='grid grid-rows-[max-content,1fr] flex-col w-screen h-screen overflow-hidden'>
            <AppHeader
                showSearch={false}
                activeTab='transactions'
                userName={`${convertToTitleCase(first_name)} ${convertToTitleCase(last_name)}`}
            />
            <div className='w-full px-6 py-4 gap-6'>
                <article className='size-full rounded-xl bg-[#1C1C1C] px-8 pb-5 overflow-y-scroll'>
                    <TransactionsTable />
                </article>
            </div>
        </div>
    )
}

export default HistoryPage