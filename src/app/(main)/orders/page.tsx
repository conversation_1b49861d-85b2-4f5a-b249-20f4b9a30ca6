"use client";

import React from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui";
import {
  OrdersCustomersTable,
  OrdersHistoryTable,
} from "./misc/components/tables";
import { OrderManagemementPipeline } from "./misc/components";
import { useCompanyStore } from "@/stores";
import AppHeader from "@/components/layout/BaseAppHeader";
import { useGetBranchOrdersPipeline } from "./misc/api";
import { convertToTitleCase } from "@/utils/strings";
import OrderManagemementPipelineInstore from "./misc/components/instore/OrderManagemementPipeline";

interface TabProps {
  value: string;
  label: string;
  component: React.ReactNode;
}

const OrdersPage = () => {
  const { company, branch, first_name, last_name } = useCompanyStore();

  const tabsArray: TabProps[] = [
    {
      value: "order__pipeline__web__store",
      label: "Web Store Orders",
      component: (
        <OrderManagemementPipeline branch_id={branch} company_id={company} />
      ),
    },
    {
      value: "order__pipeline__in__store",
      label: "In-Store Orders",
      component: (
        <OrderManagemementPipelineInstore
          branch_id={branch}
          company_id={company}
        />
      ),
    },
    {
      value: "order__customers",
      label: "Customers",
      component: <OrdersCustomersTable branchId={branch} />,
    },
    {
      value: "order__history",
      label: "History",
      component: <OrdersHistoryTable branchId={branch} />,
    },
    {
      value: "order__pipeline__config",
      label: "Pipeline Config",
      component: <></>,
    },
    {
      value: "order__web__visits",
      label: "Web Visits",
      component: <></>,
    },
  ];

  return (
    <div className="grid grid-rows-[max-content,1fr] flex-col w-screen h-screen overflow-hidden">
      <AppHeader
        userName={`${convertToTitleCase(first_name)} ${convertToTitleCase(
          last_name
        )}`}
      />

      <div className="w-full px-6 py-4 pt-0 gap-6 overflow-y-hidden">
        <div className="h-0.5 w-full bg-[#262729] mt-2.5 mb-5" />
        <Tabs
          className="size-full grid grid-rows-[max-content_1fr] p-4 bg-[#1C1C1C] rounded-xl overflow-hidden"
          defaultValue="order__pipeline__in__store"
        >
          <div className="overflow-x-auto">
            <TabsList className="flex items-center md:justify-start gap-x-4 md:gap-5 mb-1 px-6 md:px-7 lg:px-11 border-none !pt-0 bg-[#1C1C1C]">
              {tabsArray.map((tab) => (
                <TabsTrigger
                  className="whitespace-nowrap rounded-none text-[0.925rem] text-white data-[state=active]:font-medium border-transparent border-b-[3px] hover:border-b-white/20 data-[state=active]:text-white data-[state=active]:!border-b-white data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                  key={tab.value}
                  value={tab.value}
                >
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
          <div className="overflow-y-auto">
            {tabsArray.map((tab) => (
              <TabsContent
                className="mt-0 h-full w-full rounded-sm bg-[#1C1C1C] overflow-x-hidden"
                key={tab.value}
                value={tab.value}
              >
                {tab.component}
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default OrdersPage;
