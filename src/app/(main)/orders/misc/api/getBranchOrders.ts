import { useQuery } from "@tanstack/react-query";
// import { OrderMgtDataResult } from "../types";
import { managementAxios } from "@/lib/axios";
import { IOrder, IOrderDetail } from "../types/instore";

export const getBranchOrders = async (
  branchId: string,
  companyId: string
) => {
  const response = await managementAxios.get(`/api/v1/sales/orders`,{
    params: {
      company: companyId,
      branch: branchId
    }
  });

  return response.data as IOrder;
};

export const useGetBranchOrders = (
  branchId: string,
  companyId: string
) => {
 
  
  return useQuery({
    queryKey: ["branch-order", branchId, companyId],
    queryFn: () => getBranchOrders(branchId, companyId),
    enabled: !!branchId,
  });
};


export const getBranchOrderDetails = async (
  branchId: string,
  companyId: string,
  batch_id: string
) => {
  const response = await managementAxios.get(`/api/v1/sales/orders`,{
    params: {
      company: companyId,
      branch: branchId,
      batch_id
    }
  });

  return response.data as IOrderDetail;
};

export const useGetBranchOrderDetails = (
  branchId: string,
  companyId: string,
  batch_id: string,
  isDrawerOpen?: boolean
) => {
 
  
  return useQuery({
    queryKey: ["branch-order-details", branchId, companyId, batch_id],
    queryFn: () => getBranchOrderDetails(branchId, companyId, batch_id),
    enabled: !!branchId && (isDrawerOpen && isDrawerOpen),
  });
};
