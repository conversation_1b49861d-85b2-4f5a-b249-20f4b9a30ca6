import { useMutation } from '@tanstack/react-query';
import { managementAxios } from '@/lib/axios';



export type cancelOrderDTO = {
    order_ids: string[];
}

export interface IcancelOrderDTO {
    company: string
    branch: string
    batch_id: string
    action_type: string
  }
  
const cancelOrder = async (cancelOrderDto: cancelOrderDTO) => {
    const response = await managementAxios.post(
        `/orders/cancel_order/`,
        cancelOrderDto,

    );
    return response.data
};

export const UseCancelOrder = () => {
    return useMutation({
        mutationFn: cancelOrder,

    });
};
const cancelOrderFullfilment = async (cancelOrderDto: IcancelOrderDTO) => {
    const response = await managementAxios.put(
        `/api/v1/sales/register/`,
        cancelOrderDto,

    );
    return response.data
};

export const UseCancelOrderFullfilment = () => {
    return useMutation({
        mutationFn: cancelOrderFullfilment,

    });
};
