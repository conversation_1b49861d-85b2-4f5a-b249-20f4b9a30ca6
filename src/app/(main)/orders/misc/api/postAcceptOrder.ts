import { managementAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';



export type acceptOrderDTO = {
    order_ids: string[];

}
export interface IFulfilOrderDTO {
    company: string
    branch: string
    batch_id: string
    sales: Sale[]
  }
  
  export interface Sale {
    category_id: string
    item_id: string
    quantity: number
    amount: number
  }
  

const acceptOrder = async (acceptOrderDto: acceptOrderDTO) => {
    const response = await managementAxios.post(
        `/orders/accept_order/`,
        acceptOrderDto
    );
    return response.data
};

export const UseAcceptOrder = () => {
    return useMutation({
        mutationFn: acceptOrder,

    });
};


const fulfilOrder = async (fulfilOrderDto: IFulfilOrderDTO) => {
    const response = await managementAxios.patch(
        `/api/v1/sales/orders/`,
        fulfilOrderDto
    );
    return response.data
};

export const UsefulfilOrder = () => {
    return useMutation({
        mutationFn: fulfilOrder,

    });
};
const ApproveOrder = async (approveOrderDto: Omit<IFulfilOrderDTO, "sales">) => {
    const response = await managementAxios.put(
        `/api/v1/sales/orders/`,
        approveOrderDto
    );
    return response.data
};

export const UseApproveOrder = () => {
    return useMutation({
        mutationFn: ApproveOrder,

    });
};