import { format } from 'date-fns';
import React from 'react';

import { cn } from '@/utils/classNames';
import { convertNumberToNaira } from '@/utils/currency';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import { TOrderMgtPipelineStageOrder } from '../types';



interface OrderMgtPipelineStageOrderDetailsProps {
  data: TOrderMgtPipelineStageOrder
  order_id: string
}

const OrderMgtPipelineStageOrderDetails: React.FC<OrderMgtPipelineStageOrderDetailsProps> = ({ data }) => {

  const [hours, minutes, secondsWithFraction] = data.order_time.split(':');
  const seconds = parseInt(secondsWithFraction.split('.')[0]);
  const date = new Date(data.order_date);
  date.setHours(parseInt(hours));
  date.setMinutes(parseInt(minutes));
  date.setSeconds(seconds);
  const formattedTime = format(date, 'h:mm a');


  return (
    <div className='flex flex-col gap-2.5'>
      <section className="grid gap-4 bg-[#1C1C1C] px-4 py-6 rounded-xl md:grid-cols-2">
        <p className="text-sm text-[#7D8590]">
          Order ID:{' '}
          <span className="font-medium text-white">#{data.id}</span>
        </p>

        <p className="text-sm text-[#7D8590]">
          Amount paid:{' '}
          <span className="font-medium text-white">
            N{data.amount_paid}
          </span>
        </p>

        <div className="flex flex-col gap-y-2">
          <p className="text-xs">Status:</p>
          <div className="flex items-center gap-2.5">
            <span
              className={cn(
                'rounded-md px-4 py-1.5 text-[0.8rem]',

                data.status == 'open' && 'bg-[#5B391A] text-[#FFDEBF]',
                data.status == 'CLOSED' && 'bg-[#EBF6F0]',
                data.status == 'CANCELLED' && 'bg-[#FFEDED]'
              )}
            >
              Order:
              <span
                className={cn(
                  'ml-1',

                  data.status == 'open' && 'text-[#FFAE63]',
                  data.status == 'CLOSED' && 'text-[#009444]',
                  data.status == 'CANCELLED' && 'text-[#FF5C5C]'
                )}
              >
                {convertKebabAndSnakeToTitleCase(data.status)}
              </span>
            </span>
            <span
              className={cn(
                'rounded-md px-4 py-1.5 text-[0.8rem]',
                data.payment_status == "unpaid" ? 'bg-[#571C1C]' : 'bg-[#FFEDED]'
              )}
            >
              Payment:
              <span
                className={cn(
                  'ml-1',
                  data.payment_status == 'unpaid' ? 'text-[#EF4444]' : 'text-[#009444]'
                )}
              >
                {data.payment_status ? 'Paid' : 'Unpaid'}
              </span>
            </span>
          </div>
        </div>

        <div className="flex flex-col gap-y-2">
          <p className="text-xs">Stage:</p>

          <span
            className={cn(
              'max-w-max rounded-md px-5 py-2 text-xs',
              '!bg-[#5B391A] !text-[#FFAE63]'
            )}
          >
            New orders
          </span>
        </div>
      </section>

      <section className="grid gap-4 bg-[#1C1C1C] px-4 py-6 rounded-xl md:grid-cols-2">
        <div className="flex flex-col">
          <p className="text-xs text-[#7D8590]">Customer&apos;s name</p>
          <p className="text-sm text-white">{`${data.buyer.first_name} ${data.buyer.last_name}`}</p>
        </div>
        <div className="flex flex-col">
          <p className="text-xs text-[#7D8590]">Phone no</p>
          <p className="text-sm text-white">{data.buyer.phone_number}</p>
        </div>
        <div className="flex flex-col">
          <p className="text-xs text-[#7D8590]">Email</p>
          <p className="text-sm text-white">{data.buyer.email}</p>
        </div>
        <div className="flex flex-col">
          <p className="text-xs text-[#7D8590]">Channel</p>
          <p className="text-sm text-white">{data.additional_information}</p>
        </div>
        <div className="flex flex-col">
          <p className="text-xs text-[#7D8590]">Time</p>
          <p className="text-sm text-white">
            {formattedTime}
          </p>
        </div>
        <div className="flex flex-col">
          <p className="text-xs text-[#7D8590]">Date</p>
          <p className="text-sm text-white">
            {format(new Date(data.order_date), 'dd-MM-yyyy')}
          </p>
        </div>
      </section>

      <section className="flex flex-col gap-2 bg-[#1C1C1C] px-4 py-6 rounded-xl">
        <h6 className="mb-3 text-xs font-semibold text-white">
          Order Details
        </h6>
        <div className="grid grid-cols-[1fr_0.3fr_0.5fr] justify-between gap-y-4 md:gap-4">
          <p className="text-xs text-[#7D8590]">Item</p>
          <p className="text-xs text-[#7D8590]">Quantity</p>
          <p className="text-xs text-[#7D8590]">Price</p>

          {
            data.order_products.map((item, index) => (
              <React.Fragment key={index}>
                <div className="flex items-center gap-2 md:gap-4">
                  <div className="aspect-[16/9] w-[25%] max-w-[85px]">
                    {
                      item.product_img ?
                        <img
                          alt="item image"
                          className="h-full w-full rounded-md object-cover !text-xs"
                          height={25}
                          src={item.product_img || ""}
                          width={25}
                        />
                        :
                        <div className='size-full bg-black'>

                        </div>
                    }
                  </div>
                  <p className="text-xs text-[#7D8590]">{item.product_name}</p>
                </div>

                <p className="text-sm font-semibold text-white self-center">
                  {item.quantity}
                </p>

                <p className="text-sm font-semibold text-white self-center">
                  {convertNumberToNaira(parseInt(item.price), true)}
                </p>
              </React.Fragment>
            ))
          }
        </div>
      </section>

      <section className="bg-[#1C1C1C] px-4 py-6 rounded-xl">
        <h6 className="mb-3 text-xs font-semibold text-white">
          Shipping Details
        </h6>
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-1.5">
            <p className="text-xs text-[#7D8590]">Address</p>
            <p className="text-sm font-medium text-white">
              {data.shipping}
            </p>
          </div>
          <div className="flex flex-col gap-1.5">
            <p className="text-xs text-[#7D8590]">Postal Code</p>
            <p className="text-sm font-medium text-white">
              {data.shipping}
            </p>
          </div>
          <div className="flex flex-col gap-1.5">
            <p className="text-xs text-[#7D8590]">Shipping Status</p>
            <p className="max-w-max rounded-md bg-main-bg px-4 py-1.5 text-xs text-main-solid">
              {data.is_delivered ? 'Delivered' : 'Not Delivered'}
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default OrderMgtPipelineStageOrderDetails;
