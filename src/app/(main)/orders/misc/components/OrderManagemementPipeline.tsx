
'use client'
import React, { useState, FC, useRef, useEffect, Suspense } from "react";
import { OrderMgtPipelineStageDetails, TOrderMgtPipelineStageOrder } from "../types";
import { cn } from "@/utils/classNames";
import OrderMgtPipelineStage from "./OrderMgtPipelineStage";
import { Spinner } from "@/components/icons";
import { MoveLeft } from "../icons";
import { useGetBranchOrdersPipeline } from "../api";
import { useCompanyStore } from "@/stores";
// import Stage from "./Stage";
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

interface OrderManagemementPipelineProps {
    company_id: string;
    branch_id: string;
}
const OrderManagemementPipeline: FC<OrderManagemementPipelineProps> = ({ branch_id }) => {

    const { branch } = useCompanyStore()
    const { data: pipelineData, isLoading: isLoadingPipelineData, isFetching: isFetchingPipelineData, refetch: refetchPipelineData } = useGetBranchOrdersPipeline(branch || "");
    const containerRef = useRef<HTMLDivElement | null>(null)
    const [showMoveLeft, setShowMoveLeft] = useState(false);
    const [showMoveRight, setShowMoveRight] = useState(false);
    const [filterUrl, setFilterUrl] = useState<string>('')

    const [allOrders, setAllOrders] = useState<TOrderMgtPipelineStageOrder[]>([]);


    useEffect(() => {
        if (pipelineData) {
            setAllOrders(pipelineData.stages.map((stage) => stage.data).flat());
        }
    }, [pipelineData]);


    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const handleScroll = () => {
            const showLeft = container.scrollLeft > 0;
            const showRight = container.scrollLeft + container.clientWidth < container.scrollWidth;
            setShowMoveLeft(showLeft);
            setShowMoveRight(showRight);
        };

        container.addEventListener('scroll', handleScroll);
        handleScroll();

        return () => {
            container.removeEventListener('scroll', handleScroll);
        };
    }, [pipelineData]);



    const MoveToLeft = () => {
        if (containerRef.current) {
            containerRef.current.scrollLeft -= 500;
        }
    };

    const MoveToRight = () => {
        if (containerRef.current) {
            containerRef.current.scrollLeft += 500;
        }
    };


    const onDragEnd = () => {
        // logic for reordering
        refetchPipelineData()

    }

    return (
        <div className="relative h-full max-h-full overflow-hidden w-full">
            {
                isLoadingPipelineData ?
                    <div className='relative flex items-center justify-center h-full w-full' >
                        <Spinner color="white" />
                    </div>
                    :
                    <div className="relative flex h-full w-full gap-3 overflow-x-scroll" ref={containerRef}>
                        <div
                            className={cn(
                                'overflow-hidden rounded-full',
                            )}
                        >
                            <div className={cn("absolute top-0 bg-[#F8F9FB] h-1 w-full overflow-hidden",
                                isFetchingPipelineData && !isLoadingPipelineData && 'bg-main-solid/20'
                            )}>

                                <div className={cn("h-full w-full origin-[0_50%] animate-indeterminate-progress rounded-full bg-main-solid opacity-0 transition-opacity", isFetchingPipelineData && !isLoadingPipelineData && 'opacity-100')}></div>
                            </div>
                        </div>

                        <Suspense fallback={<Spinner color='white' />} key={"ORDER_PIPELINE_SUSPENSE_BOUNDARY"}>
                            <DragDropContext onDragEnd={onDragEnd}>
                                <div className='relative h-full w-full overflow-x-scroll transition-all scroll-smooth duration-200' >

                                    < div className='h-full flex items-stretch overflow-x-scroll transition-all scroll-smooth duration-200' ref={containerRef}>
                                        {
                                            pipelineData?.stages.sort((a, b) => a.order - b.order)?.map((stage: OrderMgtPipelineStageDetails) => (
                                                <OrderMgtPipelineStage
                                                    allStages={pipelineData?.stages?.sort((a, b) => a.order - b.order)}
                                                    currentStage={stage}
                                                    branch_id={branch_id}
                                                    data={stage}
                                                    filterUrl={filterUrl}
                                                    key={stage.stage_id}
                                                    pipeline_id={pipelineData?.pipeline_id!}
                                                    refetchPipelineData={refetchPipelineData}
                                                    setFilterUrl={setFilterUrl}


                                                />
                                            ))
                                        }
                                    </div>

                                    {/* <aside className={cn('absolute bottom-0 right-0 flex items-center gap-4 p-6', !showMoveLeft && !showMoveRight && "hidden")}> */}
                                    <aside className={cn('absolute bottom-0 right-0 flex items-center gap-4 p-6', !showMoveLeft && !showMoveRight && "")}>
                                        <button className='bg-white hover:bg-main-bg border-main-solid border rounded-full block aspect-square p-2.5 disabled:opacity-30' disabled={!showMoveLeft} onClick={MoveToLeft}>
                                            <MoveLeft className=' scale-90' />
                                        </button>
                                        <button className='bg-white hover:bg-main-bg border-main-solid border rounded-full block aspect-square p-2.5 disabled:opacity-30' disabled={!showMoveRight} onClick={MoveToRight}>
                                            <MoveLeft className='rotate-180 scale-90' />
                                        </button>
                                    </aside>
                                </div>
                            </DragDropContext>
                        </Suspense >

                    </div>
            }
            <aside className={cn('absolute bottom-0 right-0 flex items-center gap-4 p-6', !showMoveLeft && !showMoveRight && "")}>
                <button className='bg-white hover:bg-main-bg border-main-solid border rounded-full block aspect-square p-2.5 disabled:opacity-30' disabled={!showMoveLeft} onClick={MoveToLeft}>
                    <MoveLeft className=' scale-90' />
                </button>
                <button className='bg-white hover:bg-main-bg border-main-solid border rounded-full block aspect-square p-2.5 disabled:opacity-30' disabled={!showMoveRight} onClick={MoveToRight}>
                    <MoveLeft className='rotate-180 scale-90' />
                </button>
            </aside>
        </div>
    );
};

export default OrderManagemementPipeline;

