"use client";
import React from "react";
import  moment  from "moment";
// import toast from 'react-hot-toast';
import { useBooleanStateControl } from "@/hooks";

import { TOrderMgtPipelineStageOrder } from "../../types";
import OrderDetailsDrawer from "./OrderMgtPipelineStageOrderDrawer";

import { cn } from "@/utils/classNames";
import { Order } from "../../types/instore";
import { convertNumberToNaira } from "@/utils/currency";

interface ItemProps {
  order: Order;
  className?: string;
  allOrdersId?: string[];
  allOrders?: TOrderMgtPipelineStageOrder[];
  index: number;
  stage: string;
  branch_id: string;
  selectedOrders: string[];
  setSelectedOrders: React.Dispatch<React.SetStateAction<string[]>>;
  refetchJobData?: () => void;
  nextStageId?: string;
  pipelineId?: string;
}

export enum StatusEnum {
  NewOrder = "New Order",
  Fulfilled = "Fulfilled",
  Completed = "Completed",
  Cancelled = "Cancelled",
}
const OrderMgtPipelineStageOrderCard: React.FC<ItemProps> = ({
  order,

  className,
}) => {
  const {
    created_at: order_date,
    order_stage: status,
    sales_tag,
    batch_id,
    total_sales_amount: amount_paid,
  } = order;

  const {
    state: isDrawerOpen,
    setTrue: openDrawer,
    setFalse: closeDrawer,
  } = useBooleanStateControl(false);

  const StatusComp = ({ status }: { status: StatusEnum }) => {
    switch (status) {
      case StatusEnum.Cancelled:
        return (
          <div className="flex items-center space-x-2 text-[#EF4444]">
            {/* Red Icon */}
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M7.16667 1.63484C7.63334 1.24151 8.38667 1.24151 8.84 1.63484L9.89334 2.53484C10.0933 2.70151 10.4733 2.84151 10.74 2.84151H11.8733C12.58 2.84151 13.16 3.42151 13.16 4.12818V5.26151C13.16 5.52818 13.3 5.90151 13.4667 6.10151L14.3667 7.15484C14.76 7.62151 14.76 8.37484 14.3667 8.82818L13.4667 9.88151C13.3 10.0815 13.16 10.4548 13.16 10.7215V11.8548C13.16 12.5615 12.58 13.1415 11.8733 13.1415H10.74C10.4733 13.1415 10.1 13.2815 9.9 13.4482L8.84667 14.3482C8.38 14.7415 7.62667 14.7415 7.17334 14.3482L6.12 13.4482C5.92 13.2815 5.54 13.1415 5.28 13.1415H4.11334C3.40667 13.1415 2.82667 12.5615 2.82667 11.8548V10.7148C2.82667 10.4548 2.69334 10.0748 2.52667 9.88151L1.62667 8.82151C1.24 8.36151 1.24 7.61484 1.62667 7.15484L2.52667 6.09484C2.69334 5.89484 2.82667 5.52151 2.82667 5.26151V4.13484C2.82667 3.42818 3.40667 2.84818 4.11334 2.84818H5.26667C5.53334 2.84818 5.90667 2.70818 6.10667 2.54151L7.16667 1.63484Z"
                fill="#EF4444"
              />
              <path
                d="M8.00004 11.2474C7.63337 11.2474 7.33337 10.9474 7.33337 10.5807C7.33337 10.2141 7.62671 9.91406 8.00004 9.91406C8.36671 9.91406 8.66671 10.2141 8.66671 10.5807C8.66671 10.9474 8.37337 11.2474 8.00004 11.2474Z"
                fill="#EF4444"
              />
              <path
                d="M8 9.14854C7.72667 9.14854 7.5 8.92188 7.5 8.64854V5.42188C7.5 5.14854 7.72667 4.92188 8 4.92188C8.27333 4.92188 8.5 5.14854 8.5 5.42188V8.64188C8.5 8.92188 8.28 9.14854 8 9.14854Z"
                fill="#EF4444"
              />
            </svg>

            <span>{status}</span>
          </div>
        );
      case StatusEnum.NewOrder:
        return (
          <div className="flex items-center space-x-2 text-[#EF4444]">
            {/* Red Icon */}
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M7.16667 1.63484C7.63334 1.24151 8.38667 1.24151 8.84 1.63484L9.89334 2.53484C10.0933 2.70151 10.4733 2.84151 10.74 2.84151H11.8733C12.58 2.84151 13.16 3.42151 13.16 4.12818V5.26151C13.16 5.52818 13.3 5.90151 13.4667 6.10151L14.3667 7.15484C14.76 7.62151 14.76 8.37484 14.3667 8.82818L13.4667 9.88151C13.3 10.0815 13.16 10.4548 13.16 10.7215V11.8548C13.16 12.5615 12.58 13.1415 11.8733 13.1415H10.74C10.4733 13.1415 10.1 13.2815 9.9 13.4482L8.84667 14.3482C8.38 14.7415 7.62667 14.7415 7.17334 14.3482L6.12 13.4482C5.92 13.2815 5.54 13.1415 5.28 13.1415H4.11334C3.40667 13.1415 2.82667 12.5615 2.82667 11.8548V10.7148C2.82667 10.4548 2.69334 10.0748 2.52667 9.88151L1.62667 8.82151C1.24 8.36151 1.24 7.61484 1.62667 7.15484L2.52667 6.09484C2.69334 5.89484 2.82667 5.52151 2.82667 5.26151V4.13484C2.82667 3.42818 3.40667 2.84818 4.11334 2.84818H5.26667C5.53334 2.84818 5.90667 2.70818 6.10667 2.54151L7.16667 1.63484Z"
                fill="#EF4444"
              />
              <path
                d="M8.00004 11.2474C7.63337 11.2474 7.33337 10.9474 7.33337 10.5807C7.33337 10.2141 7.62671 9.91406 8.00004 9.91406C8.36671 9.91406 8.66671 10.2141 8.66671 10.5807C8.66671 10.9474 8.37337 11.2474 8.00004 11.2474Z"
                fill="#EF4444"
              />
              <path
                d="M8 9.14854C7.72667 9.14854 7.5 8.92188 7.5 8.64854V5.42188C7.5 5.14854 7.72667 4.92188 8 4.92188C8.27333 4.92188 8.5 5.14854 8.5 5.42188V8.64188C8.5 8.92188 8.28 9.14854 8 9.14854Z"
                fill="#EF4444"
              />
            </svg>

            <span>{status}</span>
          </div>
        );

      case StatusEnum.Fulfilled:
        return (
          <div className="flex items-center space-x-2 text-[#C78C00]">
            {/* Yellow Icon */}
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M7.16667 1.63484C7.63334 1.24151 8.38667 1.24151 8.84 1.63484L9.89334 2.53484C10.0933 2.70151 10.4733 2.84151 10.74 2.84151H11.8733C12.58 2.84151 13.16 3.42151 13.16 4.12818V5.26151C13.16 5.52818 13.3 5.90151 13.4667 6.10151L14.3667 7.15484C14.76 7.62151 14.76 8.37484 14.3667 8.82818L13.4667 9.88151C13.3 10.0815 13.16 10.4548 13.16 10.7215V11.8548C13.16 12.5615 12.58 13.1415 11.8733 13.1415H10.74C10.4733 13.1415 10.1 13.2815 9.9 13.4482L8.84667 14.3482C8.38 14.7415 7.62667 14.7415 7.17334 14.3482L6.12 13.4482C5.92 13.2815 5.54 13.1415 5.28 13.1415H4.11334C3.40667 13.1415 2.82667 12.5615 2.82667 11.8548V10.7148C2.82667 10.4548 2.69334 10.0748 2.52667 9.88151L1.62667 8.82151C1.24 8.36151 1.24 7.61484 1.62667 7.15484L2.52667 6.09484C2.69334 5.89484 2.82667 5.52151 2.82667 5.26151V4.13484C2.82667 3.42818 3.40667 2.84818 4.11334 2.84818H5.26667C5.53334 2.84818 5.90667 2.70818 6.10667 2.54151L7.16667 1.63484Z"
                fill="#C78C00"
              />
            </svg>

            <span>{StatusEnum.Fulfilled}</span>
          </div>
        );

      case StatusEnum.Completed:
      default:
        return (
          <div className="flex items-center space-x-2 text-[#0ADCAC]">
            {/* Teal Icon */}
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M7.16667 1.63484C7.62667 1.24151 8.38 1.24151 8.84667 1.63484L9.9 2.54151C10.1 2.71484 10.4733 2.85484 10.74 2.85484H11.8733C12.58 2.85484 13.16 3.43484 13.16 4.14151V5.27484C13.16 5.53484 13.3 5.91484 13.4733 6.11484L14.38 7.16818C14.7733 7.62818 14.7733 8.38151 14.38 8.84818L13.4733 9.90151C13.3 10.1015 13.16 10.4748 13.16 10.7415V11.8748C13.16 12.5815 12.58 13.1615 11.8733 13.1615H10.74C10.48 13.1615 10.1 13.3015 9.9 13.4748L8.84667 14.3815C8.38667 14.7748 7.63334 14.7748 7.16667 14.3815L6.11334 13.4748C5.91334 13.3015 5.54 13.1615 5.27334 13.1615H4.12C3.41334 13.1615 2.83334 12.5815 2.83334 11.8748V10.7348C2.83334 10.4748 2.69334 10.1015 2.52667 9.90151L1.62667 8.84151C1.24 8.38151 1.24 7.63484 1.62667 7.17484L2.52667 6.11484C2.69334 5.91484 2.83334 5.54151 2.83334 5.28151V4.13484C2.83334 3.42818 3.41334 2.84818 4.12 2.84818H5.27334C5.53334 2.84818 5.91334 2.70818 6.11334 2.53484L7.16667 1.63484Z"
                fill="#0ADCAC"
              />
              <path
                d="M7.19345 10.1129C7.06012 10.1129 6.93345 10.0595 6.84012 9.9662L5.22679 8.35286C5.03345 8.15953 5.03345 7.83953 5.22679 7.6462C5.42012 7.45286 5.74012 7.45286 5.93345 7.6462L7.19345 8.9062L10.0601 6.03953C10.2535 5.8462 10.5735 5.8462 10.7668 6.03953C10.9601 6.23286 10.9601 6.55286 10.7668 6.7462L7.54679 9.9662C7.45345 10.0595 7.32679 10.1129 7.19345 10.1129Z"
                fill="#0ADCAC"
              />
            </svg>

            <span>{StatusEnum.Completed}</span>
          </div>
        );
    }
  };

  const removalTheme = "bg-[#EF4444] text-[#fff]"
  const reductionTheme = "bg-[#C78C00] text-[#000]"

  return (
    <article
      className={cn(
        "relative grid grid-cols-[1fr,max-content] gap-1.5 bg-[#1C1C1C] p-4 rounded-xl !text-[0.85rem] mb-4 w-full max-w-[400px]",
        order.bar_alert && order.reduction && "bg-[#2E250F] border-[.3px] border-[#C78C00]",
        order.bar_alert && order.removal && "bg-[#220E0E] border-[.3px] border-[#EF4444]",
        className
      )}
    >
      {order.bar_alert && (
        <div className="absolute left-1/2 transform -translate-x-1/2 inline-block">
          <div
            className={cn(order.reduction && reductionTheme, order.removal && removalTheme,  "text-center px-5 py-1 text-xs max-w-fit mx-auto")}
            style={{
              clipPath: "polygon(0% 0%, 100% 0%, 90% 100%, 10% 100%)",
            }}
          >
            Item update request
          </div>
        </div>
      )}

      <section
        className={cn("flex flex-col gap-2.5", order.bar_alert && "mt-4")}
      >
        <div className="grid grid-cols-[1fr,0.8fr] gap-8 text-[0.675rem]">
          <p className="flex flex-col text-[#7D8590]">
            <span className="text-white font-medium truncate">#{batch_id}</span>
            Order ID
          </p>
          <p className="flex flex-col text-[#7D8590] text-left truncate">
            <span className="text-white font-medium truncate">
              {moment(order_date).format('LLL')}
            </span>
            Time
          </p>
        </div>
        <div className="grid grid-cols-[1fr,0.8fr] gap-8 text-[0.675rem]">
          <p className="flex flex-col text-[#7D8590]">
            <span className="text-white font-medium truncate">
              {sales_tag ?? "N/A"}
            </span>
            Tag
          </p>
          <p className="flex flex-col text-[#7D8590] text-left truncate">
            <span className="text-white font-medium truncate">
              {convertNumberToNaira(amount_paid)}
            </span>
            Amount
          </p>
        </div>
        <div className="grid grid-cols-[1fr,0.8fr] gap-8 text-[0.675rem] items-center">
          <StatusComp status={status as StatusEnum} />
          <p className="flex flex-col text-[#7D8590]">
            <OrderDetailsDrawer
              closeDrawer={closeDrawer}
              isDrawerOpen={isDrawerOpen}
              openDrawer={openDrawer}
              currentOrder={order}
            />
          </p>
        </div>
      </section>

     
    </article>
  );
};

export default OrderMgtPipelineStageOrderCard;
