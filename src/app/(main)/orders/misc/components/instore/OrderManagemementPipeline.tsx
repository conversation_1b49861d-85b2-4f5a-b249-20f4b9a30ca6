"use client";

import React, { Suspense, useEffect, useRef, useState } from "react";
import { DragDropContext } from "@hello-pangea/dnd";

import OrderMgtPipelineStage from "./OrderMgtPipelineStage";
// import { OrderMgtPipelineStageDetails } from "../../types";
import { cn } from "@/utils/classNames";
import { Spinner } from "@/components/icons";
import { MoveLeft } from "../../icons";
// import { useGetBranchOrdersPipeline } from "../../api";
import { useGetBranchOrders } from "../../api/getBranchOrders";
import { IOrder, PipelineStages } from "../../types/instore";

interface PipelineProps {
  branch_id: string;
  company_id: string;
}

const OrderManagemementPipelineInstore: React.FC<PipelineProps> = ({
  branch_id,
  company_id,
}) => {
  const { data: ordersData, isLoading: isLoadingOrder, error } = useGetBranchOrders(
    branch_id,
    company_id
  );


  //////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////////
  /////////////                        PIPELINE LAYOUT                           ///////////////
  //////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////////
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [showMoveLeft, setShowMoveLeft] = useState(false);
  const [showMoveRight, setShowMoveRight] = useState(false);
  const [filterUrl, setFilterUrl] = useState<string>("");
  const MARKETING_STAGE_ORDER = [
    "New Order",
    "Fulfilled",
    "Completed",
    "Cancelled",
  ];
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const showLeft = container.scrollLeft > 0;
      const showRight =
        container.scrollLeft + container.clientWidth < container.scrollWidth;
      setShowMoveLeft(showLeft);
      setShowMoveRight(showRight);
    };

    container.addEventListener("scroll", handleScroll);
    handleScroll();
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [containerRef.current?.scrollWidth, containerRef.current?.clientWidth]);

  const MoveToLeft = () => {
    if (containerRef.current) {
      containerRef.current.scrollLeft -= 500;
    }
  };

  const MoveToRight = () => {
    if (containerRef.current) {
      containerRef.current.scrollLeft += 500;
    }
  };

  const onDragEnd = () => {
    // logic for reordering
  };

  return (
    <div className="h-full">
      {isLoadingOrder ? (
        <div className="relative flex items-center justify-center h-full w-full">
          <Spinner color="white" />
        </div>
      ) : !isLoadingOrder && (error as any)?.response?.status === 403 ? (
        <div className="relative h-full w-full grid  place-content-center">
          <h4>You do not have access to view, contact admin.</h4>
        </div>
      ) : (
        <Suspense
          fallback={<Spinner color="white" />}
          key={"JOB_PIPELINE_SUSPENSE_BOUNDARY"}
        >
          <DragDropContext onDragEnd={onDragEnd}>
            <div className="relative h-full w-full overflow-x-scroll transition-all scroll-smooth duration-200">
              <div
                className="h-full flex items-stretch overflow-x-scroll transition-all scroll-smooth duration-200"
                ref={containerRef}
              >
                {MARKETING_STAGE_ORDER.map((stage) => {
                  const count = (ordersData as IOrder)?.data.pipeline_stages[
                    stage as keyof PipelineStages
                  ];
                  return (
                    <OrderMgtPipelineStage
                      key={stage}
                      stageName={stage}
                      branch_id={branch_id}
                      itemCount={count ?? 0}
                      data={ordersData?.data.orders}
                      filterUrl={filterUrl}
                      setFilterUrl={setFilterUrl}
                    />
                  );
                })}
              </div>

              {/* <aside className={cn('absolute bottom-0 right-0 flex items-center gap-4 p-6', !showMoveLeft && !showMoveRight && "hidden")}> */}
              <aside
                className={cn(
                  "absolute bottom-0 right-0 flex items-center gap-4 p-6",
                  !showMoveLeft && !showMoveRight && ""
                )}
              >
                <button
                  className="bg-white hover:bg-main-bg border-main-solid border rounded-full block aspect-square p-2.5 disabled:opacity-30"
                  disabled={!showMoveLeft}
                  onClick={MoveToLeft}
                >
                  <MoveLeft className=" scale-90" />
                </button>
                <button
                  className="bg-white hover:bg-main-bg border-main-solid border rounded-full block aspect-square p-2.5 disabled:opacity-30"
                  disabled={!showMoveRight}
                  onClick={MoveToRight}
                >
                  <MoveLeft className="rotate-180 scale-90" />
                </button>
              </aside>
            </div>
          </DragDropContext>
        </Suspense>
      )}
    </div>
  );
};

export default OrderManagemementPipelineInstore;
