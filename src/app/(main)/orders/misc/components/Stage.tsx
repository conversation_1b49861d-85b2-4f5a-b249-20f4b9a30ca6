import React, { useMemo, useRef, useState } from 'react'
import { UseCancelOrder, UseMoveOrdertoNextStage } from '../api';
import { OrderMgtPipelineStageDetails, TOrderMgtPipelineStageOrder } from '../types';
import { useBooleanStateControl } from '@/hooks';
import toast from 'react-hot-toast';
import { cn } from '@/utils/classNames';

interface ColumnProps {
    orders: TOrderMgtPipelineStageOrder[];
    column: string;
    setOrders: React.Dispatch<React.SetStateAction<TOrderMgtPipelineStageOrder[]>>;
    branchId: string;
    pipelineId: string;
    refetchPipelineData: () => void
    allStages: OrderMgtPipelineStageDetails[]
}
const Stage: React.FC<ColumnProps> = ({
    column,
    orders,
    setOrders,
    branchId,
    pipelineId,
    refetchPipelineData,
    allStages
}) => {
    const [active, setActive] = useState(false);
    const { mutate: moveOrderToNewStage, } = UseMoveOrdertoNextStage()
    const { mutate: cancelTheOrder, isPending: isCancellingOrder } = UseCancelOrder()
    const {
        state: isSearchBoxOpen,
        setFalse: closeSearchBox,
        toggle: toggleSearchBox
    } = useBooleanStateControl(false)


    const searchTextBoxRef = useRef<HTMLInputElement | null>(null);
    const [searchText, setSearchText] = React.useState('')
    const currentStageOrders = useMemo(() => {
        return orders.filter((order) => order.current_stage.name === column);
    }, [orders, column]);

    const ordersToDisplay = useMemo(() => {
        if (searchText && searchText.trim() !== "") {
            return orders.filter((order) =>
                order.current_stage.name === column && (
                    order.contact_name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.contact_phone_number?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.first_name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.last_name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.email?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.order_products.some((product) => product.product_name.toLowerCase().includes(searchText.toLowerCase())) ||
                    order.channels?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.status?.toLowerCase().includes(searchText) ||
                    order.buyer.address?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.city?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.state?.toLowerCase().includes(searchText.toLowerCase()) ||
                    order.buyer.country?.toLowerCase().includes(searchText.toLowerCase())
                ))
        } else {
            return orders.filter((order) => order.current_stage.name === column);
        }
    }, [searchText, orders, column]);

    const [selectedOrders, setSelectedOrders] = React.useState<string[]>([])
    const currentStage = allStages.find((stage) => stage.stage_name === column) || {} as OrderMgtPipelineStageDetails
    const nextStageId = allStages[(currentStage?.position || 0)]?.stage_id







    // /////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////                          BOOLEAN STATES                     //////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////
    const {
        state: isConfirmProgressSelectedOrdersModalOpen,
        setTrue: openConfirmProgressSelectedOrdersModal,
        setFalse: closeConfirmProgressSelectedOrdersModal,
    } = useBooleanStateControl(false)
    const {
        state: isConfirmCancelSelectedOrdersModalOpen,
        setTrue: openConfirmCancelSelectedOrdersModal,
        setFalse: closeConfirmCancelSelectedOrdersModal,
    } = useBooleanStateControl(false)
    const {
        state: isConfirmCancelRangeOrdersModalOpen,
        setTrue: openConfirmCancelRangeOrdersModal,
        setFalse: closeConfirmCancelRangeOrderRangeModal,
    } = useBooleanStateControl(false)
    const {
        state: isConfirmProgressRangeOrdersModalOpen,
        setTrue: openConfirmProgressRangeOrdersModal,
        setFalse: closeConfirmProgressRangeOrderal,
    } = useBooleanStateControl(false)

    // React.useEffect(() => {
    //     if (isSearchBoxOpen && searchTextBoxRef.current) {
    //         searchTextBoxRef.current.focus();
    //     }
    // }, [isSearchBoxOpen]);



    // /////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////                          ORDER ACTIONS                      //////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////
    const [startProgressIndex, setStartProgressIndex] = useState(0)
    const [endProgressIndex, setEndProgressIndex] = useState(0)
    const { mutate: moveOrderToNextStage, isPending: isMovingOders } = UseMoveOrdertoNextStage()


    const progressSelectedOrders = () => {
        if (nextStageId) {
            moveOrderToNextStage({
                new_stage_id: nextStageId,
                order_ids: selectedOrders,
                pipeline_id: pipelineId

            }, {
                onSuccess() {
                    toast.success('Orders moved successfully');
                    refetchPipelineData()
                    closeConfirmProgressSelectedOrdersModal()
                }
            })
        }
    }
    const cancelSelectedOrders = () => {
        cancelTheOrder({
            order_ids: selectedOrders,
        }, {
            onSuccess() {
                toast.success('Orders cancelled successfully');
                refetchPipelineData()
                closeConfirmCancelSelectedOrdersModal()
            }
        })
    }
    const cancelOrderRange = () => {
        if (startProgressIndex === 0 || endProgressIndex === 0) return
        if (startProgressIndex > endProgressIndex) return toast.error('Invalid range selected, start index cannot be greater than end index')
        if (endProgressIndex > currentStageOrders.length) return toast.error(`Invalid range selected, current stage has only ${currentStageOrders.length} orders`)
        const ordersToCancel = currentStageOrders.slice(startProgressIndex - 1, endProgressIndex)
        const orderIds = ordersToCancel.map((order) => order.id)
        cancelTheOrder({
            order_ids: orderIds,
        }, {
            onSuccess() {
                toast.success('Orders cancelled successfully');
                refetchPipelineData()
                closeConfirmCancelSelectedOrdersModal()
            }
        })
    }
    const progressOrderRange = () => {
        if (startProgressIndex === 0 || endProgressIndex === 0) return
        if (startProgressIndex > endProgressIndex) return toast.error('Invalid range selected, start index cannot be greater than end index')
        if (endProgressIndex > currentStageOrders.length) return toast.error(`Invalid range selected, current stage has only ${currentStageOrders.length} orders`)
        if (nextStageId) {
            const ordersToProgress = currentStageOrders.slice(startProgressIndex - 1, endProgressIndex)
            const orderIds = ordersToProgress.map((order) => order.id)
            moveOrderToNextStage({
                new_stage_id: nextStageId,
                order_ids: orderIds,
                pipeline_id: pipelineId
            }, {
                onSuccess() {
                    toast.success('Orders moved successfully');
                    refetchPipelineData()
                    closeConfirmProgressRangeOrderal()
                }
            })
        }
    }







    return (
        <div
            className={cn(" flex flex-col !shrink-0  gap-2 mt-0 pt-1 px-2 h-full overflow-y-scroll max-md:min-w-[90vw]  w-max min-w-[200px] lg:w-[400px] max-w-[425px]  transition-colors rounded-2xl", active ? "bg-main-bg" : "bg-neutral-800/0")}

        >
            
            Stage
        </div>
    )
}

export default Stage