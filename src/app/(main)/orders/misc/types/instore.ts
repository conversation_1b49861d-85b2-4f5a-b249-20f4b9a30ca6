import { StatusEnum } from "../components/instore/OrderMgtPipelineStageOrderCard"

export interface IOrder {
    status: string
    status_code: number
    data: Data
    errors: any
  }
  
  export interface Data {
    total_orders: number
    pipeline_stages: PipelineStages
    orders: Order[]
  }
  
  export interface PipelineStages {
    "New Order": number
    Completed: number
    Cancelled: number
  }
  
  export interface Order {
    created_at: string
    order_stage: string
    position: number
    sales_tag: any
    batch_id: string
    bar_alert: boolean
    reduction: boolean
    removal: boolean
    total_sales_amount: number
  }
  

  export interface IOrderDetail {
    status: string
    status_code: number
    data: OrderDetailData
    errors: any
  }
  
  export interface OrderDetailData {
    batch_id: string
    order_details: OrderDetail
  }
  
  export interface OrderDetail {
    created_at: string
    batch_id: string
    sales_tag: any
    current_stage: string
    paid: boolean
    total_cost: number
    device: string
    reduction: boolean
    removal: boolean
    customer_name: string
    customer_email: string
    customer_phone: string
    products: Product[]
    trail: Trail

  }

  export interface Trail {
    [key: string]: Item[] | Product[];
  }
  
  export interface StatusOrder {
    items: Item[] | Product[]
    action_by: string
    action_time: string
    register_sales: string
  }
  
  export interface Item {
    category: string
    quantity: number
    unit_price: number
    item_description: string
  }
  
  export interface Product {
    created_at: string
    category_id: string
    item_id: string
    item_description: string
    quantity: number
    amount: number
    price: number
    reduction: boolean
    removal: boolean
    status: StatusEnum
    bar_alert: boolean
    approved: boolean
  }
  