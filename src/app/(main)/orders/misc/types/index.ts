export type { OrderMgtPipelineStageDetails, TOrderMgtPipelineStageOrder, TOrderMgtPipelineStageOrder<PERSON>uyer, TOrderMgtBranchCustomers, TOrderMgtBranchOrderHistory, OrderMgtPipelineDataResult } from './orderManagement'
export interface IndustriesResponse {
  count: number;
  next?: null;
  previous?: null;
  results?: ResultsEntity[];
}
export interface ResultsEntity {
  id: number;
  industry: string;
  created_at: string;
  updated_at: string;
}

export interface PinVerificationResponse {
  status: boolean;
  pin: boolean;
  message: string;
}

export interface AllCustomersResponse {
  status: string;
  status_code: number;
  data: {
    message: string;
    customers: Customer[];
    count: number;
    total_customers: number;
  };
}
export type Customer = {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  email: string;
  phone: string | null;
  address: string | null;
  unique_identifier: string | null;
  branch: string;
  company: string;
  created_by: string;
  updated_by: string | null;
};

export interface AllCustomersTransactions {
  status: string;
  status_code: number;
  data: {
    message: string;
    customers: CustomersTransactions[];
    count: number;
    total_customers: number;
  };
}

export type CustomersTransactions = {
  id: string;
  customer: string;
  name: string;
  address: string;
  email: string;
  phone: string;
  branch: string;
  purchase_amount: string;
  status: string;
  created_at: string;
};

export interface AllCustomersActivityResponse {
  status: string;
  status_code: number;
  data: {
    message: string;
    customers: ActivityCustomer[];
    count: number;
    total_customers: number;
  };
}

export type ActivityCustomer = {
  customer_name: string;
  email: string;
  transactions: string;
};

