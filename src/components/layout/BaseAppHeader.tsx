"use client";

import React, { useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { format } from "date-fns";
import { Menu, Eye, LockIcon } from "lucide-react";
import Link from "next/link";

import { LinkButton, But<PERSON> } from "@/components/ui";
import {
  Paybox360,
  ScanIcon,
  CameraIcon,
  LogoutIcon,
} from "@/components/icons";
import { useCompanyStore } from "@/stores";
import { cn } from "@/utils/classNames";

interface HeaderProps {
  showSearch?: boolean;
  activeTab?: "sales" | "transactions" | "";
  userName: string;
  className?: string;
}

const AppHeader: React.FC<HeaderProps> = ({
  showSearch = false,
  activeTab = "",
  userName,
  className,
}) => {
  const [isMobileView, setIsMobileView] = useState(false);
  const [showMenuSheet, setShowMenuSheet] = useState(false);
  const { branchData } = useCompanyStore();

  useEffect(() => {
    const handleResize = () => setIsMobileView(window.innerWidth < 768);
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <header
      className={cn(
        "flex items-center justify-between gap-4 p-4 h-[8.5vh]",
        className && className
      )}
    >
      <section className="flex items-center gap-4">
        <Paybox360 />
        {!isMobileView && (
          <div className="bg-[#1E1E1E] p-1.5 rounded-lg max-h-14">
            <p className="flex flex-col p-1 px-4 rounded-lg bg-[#D9D9D91A] text-white">
              <span className="text-[0.9rem] font-medium">
                {branchData.company}
              </span>
              <span className="text-xs text-white/70">{branchData.name}</span>
            </p>
          </div>
        )}
      </section>

      {isMobileView ? (
        <Button
        variant="secondary"
        className="px-2 h-8 text-sm rounded-lg"
        onClick={() => setShowMenuSheet(true)}
      >
        Menu
      </Button>
      ) : (
        <div className="flex items-center gap-4">
          <LinkButton
            className="flex items-center gap-2"
            href={showSearch ? "/history" : "/dashboard"}
            variant="secondary"
            size="thin"
          >
            {showSearch ? "Sales record" : "Sell"}
            <Eye size={20} className={showSearch ? "" : "ml-4"} />
          </LinkButton>

          <LinkButton
            href="/transactions"
            variant="secondary"
            className="w-12"
            size="icon"
          >
            <ScanIcon />
          </LinkButton>

          <Button variant="secondary" className="w-12" size="icon">
            <CameraIcon />
          </Button>

          <LinkButton
            href="/orders"
            className="gap-2 bg-[#1E1E1E] py-4"
            variant="secondary"
            size="thin"
          >
            Orders <Eye size={20} />
          </LinkButton>

          <LinkButton
            href="/onboard/break"
            variant="secondary"
            className="w-12"
            size="icon"
          >
            <LockIcon />
          </LinkButton>

          <Button className="flex items-center gap-2 py-0" variant="unstyled">
            <div className="flex flex-col items-start gap-0">
              <p className="text-[0.925rem]">{userName}</p>
              <span className="text-muted-foreground text-left text-[0.75rem]">
                {format(new Date(), "yyyy-MM-dd")}
              </span>
            </div>
            <LinkButton
              href="/onboard/shift"
              className="flex items-center justify-center rounded-lg p-1 bg-[#3a1720ba]"
              variant="unstyled"
            >
              <LogoutIcon width={25} height={25} />
            </LinkButton>
          </Button>
        </div>
      )}

      <AnimatePresence>
        {isMobileView && showMenuSheet && (
          <motion.div
            initial={{ y: "100%" }}
            animate={{ y: 0 }}
            exit={{ y: "100%" }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-0 left-0 right-0 z-[999999999] h-[70vh] bg-[#121212] rounded-t-2xl p-4 space-y-4 overflow-y-auto"
          >
            <div className="flex justify-between items-center border-b border-white/10 pb-2">
              <h2 className="text-white text-lg font-medium">Menu</h2>
              <button
                onClick={() => setShowMenuSheet(false)}
                className="text-white text-xl"
              >
                ✕
              </button>
            </div>
            <div className="flex flex-col gap-2">
              <LinkButton href="/history" variant="secondary">
                Sales
              </LinkButton>
              <LinkButton href="/transactions" variant="secondary">
                Transactions
              </LinkButton>
              <LinkButton href="/dashboard" variant="secondary">
                Sell
              </LinkButton>
              <LinkButton href="/orders" variant="secondary">
                Orders
              </LinkButton>

              <div className="flex justify-between items-center">
                <LinkButton
                  href="/onboard/break"
                  variant="secondary"
                  className="flex items-center w-12"
                  size="icon"
                >
                  <LockIcon />
                </LinkButton>
                <Button
                  variant="unstyled"
                  className="flex items-center gap-2 py-0"
                >
                  <div className="flex flex-col items-start gap-0">
                    <p className="text-[0.925rem]">{userName}</p>
                    <span className="text-muted-foreground text-left text-[0.75rem]">
                      {format(new Date(), "yyyy-MM-dd")}
                    </span>
                  </div>
                  <Link
                    className="rounded-lg p-1 bg-[#3a1720ba]"
                    href="/onboard/shift"
                  >
                    <LogoutIcon width={20} height={20} />
                  </Link>
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default AppHeader;
