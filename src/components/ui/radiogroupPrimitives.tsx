'use client';

import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import * as React from 'react';

import { cn } from '@/utils/classNames'

const RadioGroup = React.forwardRef<
    React.ElementRef<typeof RadioGroupPrimitive.Root>,
    React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
    return (
        <RadioGroupPrimitive.Root
            className={cn('grid gap-2', className)}
            {...props}
            ref={ref}
        />
    );
});
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName;
interface RadioGroupItemProps extends React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item> {
    itemLabelClass?: string; 
    indicatorClass?: string;
}

const RadioGroupItem = React.forwardRef<
    React.ElementRef<typeof RadioGroupPrimitive.Item>,
    RadioGroupItemProps
>(({ className, children, itemLabelClass, indicatorClass, ...props }, ref) => {
    return (
        <RadioGroupPrimitive.Item
            className='flex items-center gap-2 w-full justify-between'
            ref={ref}
            {...props}
        >
            <p className={cn('grow text-[0.825rem]', itemLabelClass)}>{children}</p>
            <div
                className={cn(
                    'relative shrink-0 flex items-center justify-center h-[0.95rem] w-[0.95rem] p-[0.0725rem] rounded-full border-[1.75px] border-primary text-primary text-sm ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
                    className
                )}
            >
                <RadioGroupPrimitive.Indicator className={cn("size-2 inline-block rounded-full bg-[#755AE2]", indicatorClass)}></RadioGroupPrimitive.Indicator>
            </div>
        </RadioGroupPrimitive.Item>
    );
});

RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;

export { RadioGroup, RadioGroupItem };
