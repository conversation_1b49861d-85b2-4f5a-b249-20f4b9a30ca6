import React, { FC, ReactNode } from 'react';
import { DeleteIcon } from 'lucide-react';

import { Button, Dialog, DialogContent, DialogFooter, DialogHeader } from '.';
import { cn } from '@/utils/classNames'
import { SmallSpinner } from '../icons';
// import { StrokeClose } from '@/icons/core';

interface Props {
    isModalOpen: boolean;
    closeModal: () => void
    deleteFunction: () => void
    children: ReactNode
    title: string;
    isDeleting?: boolean
}

const ConfirmDeleteModal: FC<Props> = ({ isModalOpen, deleteFunction, closeModal, children, title, isDeleting }) => {

    return (
        <Dialog modal={true} open={isModalOpen} >

            <DialogContent
                aria-label={title}
                className={cn(
                    'rounded-[10px]',
                    'my-6 '
                )}
                style={{
                    width: '92.5%',
                    minWidth: '300px',
                }}
                onPointerDownOutside={closeModal}
            >
                <DialogHeader className={cn('max-sm:sticky top-0 z-10 bg-danger-darker')}>
                    <h5 className='text-base font-medium '>Confirm Delete</h5>
                    <Button className='rounded-xl bg-white/20 px-5' size={'sm'} variant={'unstyled'} onClick={closeModal}>
                        Close
                    </Button>
                </DialogHeader>
                <div className={cn('')}>

                    <div className='px-2'>
                        <span className='bg-danger-dark rounded-full p-2 inline-block'>
                            <DeleteIcon height={75} width={75} />
                        </span>

                        <h3 className='text-danger-dark text-xl font-medium mt-1'>{title}</h3>
                        {children}
                    </div>
                </div>


                <DialogFooter className='flex items-center justify-end gap-4 bg-danger-light w-full rounded-2xl p-5'>
                    <Button className='py-2 text-header-text hover:opacity-40 border-[1.5px] border-transparent hover:bg-white hover:border-header-text hover:text-header-text' variant="ghost" onClick={closeModal}>
                        Cancel
                    </Button>
                    <Button className='py-2 bg-danger-darker hover:!border-danger-darker hover:text-danger-darker' disabled={isDeleting} onClick={deleteFunction}>
                        Delete
                        {
                            isDeleting && <SmallSpinner />
                        }
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default ConfirmDeleteModal;