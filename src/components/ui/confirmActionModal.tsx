import React, { FC, ReactNode } from 'react';
import { <PERSON><PERSON>, Di<PERSON>Header, <PERSON><PERSON>, DialogContent, DialogFooter } from '.';
import { cn } from '@/utils/classNames'
import { Plane, Send } from 'lucide-react';
import { SmallSpinner } from '../icons';

interface Props {
    isModalOpen: boolean;
    closeModal: () => void
    confirmFunction?: () => void
    isConfirmingAction?: boolean
    children: ReactNode
    title: string;
    icon?: ReactNode
}

const ConfirmActionModal: FC<Props> = ({ isModalOpen, confirmFunction, isConfirmingAction, closeModal, children, title, icon }) => {

    return (
        <Dialog modal={true} open={isModalOpen} >
            <DialogContent
                aria-label={title}
                className={cn(
                    'rounded-[10px]',
                    'my-6 !bg-[#1C1C1C]'
                )}
                style={{
                    width: '92.5%',
                    minWidth: '300px',
                }}
                onPointerDownOutside={closeModal}
            >

                <DialogHeader className={cn('flex flex-row items-center justify-between max-sm:sticky top-0 z-10')}>
                    <h5 className='text-base font-medium '>Confirm Action</h5>
                    <Button className='rounded-xl bg-black/20 px-5' size={'sm'} variant={'unstyled'} onClick={closeModal}>
                        Close
                    </Button>
                </DialogHeader>

                <div className={cn('')}>
                    <div className='flex flex-col justify-center items-center px-2'>
                        <span className='flex items-center justify-center bg-[#D9D9D91A] rounded-full p-4'>
                            {
                                icon ?
                                    icon
                                    :
                                    <Send />
                            }
                        </span>

                        <h3 className='text-lg font-medium mt-1 font-sans text-center text-balance'>{title}</h3>
                        <div className='!text-sm !text-[#A4A8AD]'>
                            {children}
                        </div>
                    </div>
                </div>

                <DialogFooter className='flex items-center md:grid grid-cols-2 gap-3 bg-main-bg w-full rounded-2xl p-5'>
                    <Button className='py-1.5 rounded-md' onClick={closeModal}>
                        Cancel
                    </Button>
                    <Button className='py-1.5 rounded-md bg-[#1C1C1C] border-white border-[1.7px] text-white hover:bg-[#D9D9D91A]' onClick={confirmFunction}>
                        Confirm
                        {
                            isConfirmingAction &&
                            <SmallSpinner />
                        }
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default ConfirmActionModal;


