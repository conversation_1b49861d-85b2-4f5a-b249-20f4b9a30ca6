import { cn } from '@/utils/classNames';
import { cva, VariantProps } from 'class-variance-authority';
import React from 'react';
import { RadioGroup, RadioGroupItem } from './radiogroupPrimitives';
// import { FieldErrors, FieldValues } from 'react-hook-form';

const radioVariants = cva(
    'rounded-[0.67rem] border-[1.5px] border-transparent text-center max-w-max',
    {
        variants: {
            variant: {
                default: 'bg-transparent hover:border-primary hover:bg-transparent hover:text-primary',
                light: 'bg-[#F5F3FF] text-primary hover:border-primary',
                white: 'bg-white text-primary hover:bg-primary-darker hover:text-white',
                red: 'bg-red-800 text-red-100',
                outlined: '!border-[1.75px] border-1.5 border-primary bg-transparent text-primary hover:bg-primary-light-hover hover:border-primary-light-active',
                unstyled: '',
                offwhite: 'bg-[#F9F9F933] backdrop-blur-lg text-white hover:bg-white/30 hover:text-white',
            },
            size: {
                default: 'px-4 py-[0.5rem] rounded-lg',
                thin: 'px-[1.35rem] !py-[0.3rem]',
                tiny: ' px-[0.75rem] py-[0.375rem] !text-xs font-normal',
                small: 'px-4 py-[0.55rem]',
                lg: 'rounded-lg px-6 py-3',
                fullWidth: 'block w-full py-[0.835rem]',
                unstyled: '',
            },
            arrangement: {
                column: '',
                row: 'flex-wrap'
            }
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    }
)
const radioItemClass = {
    '': '',
    default: ' ',
    secondary: '',
    light: '',
    extralight: '',
    white: 'bg-white',
    red: 'b ',
    outlined: '',
    unstyled: '',
    offwhite: '!border-white',
}
const radioIndicatorClass = {
    '': '',
    default: ' ',
    secondary: '',
    light: '',
    extralight: '',
    white: 'bg-white',
    red: 'b ',
    outlined: '',
    unstyled: '',
    offwhite: '!bg-white',
}
const position = {
    column: '',
    row: '!flex flex-wrap'
}
interface RadioOption {
    value: string | boolean;
    name: string;
    icon?: React.ReactNode
}

interface RadioButtonsProps extends VariantProps<typeof radioVariants> {
    value?: string | boolean;
    fallback?: string;
    onChange: (value: string) => void;
    onclick: (value: string) => void;
    label?: string | React.ReactNode;
    // errors?: { [key: string]: { message?: string | undefined } } | FieldErrors<FieldValues>;
    options: RadioOption[];
    radioClass?: string;
    className?: string;
    labelClass?: string;
    itemClass?: string;
    containerClass?: string;
    itemsContainerClass?: string;
    name: string;
    checked?: string;
}

const RadioButtons: React.FC<RadioButtonsProps> = ({
    value: mainValue,
    fallback,
    onclick,
    onChange,
    options,
    label,
    name,
    variant,
    size,
    arrangement,
    itemClass,
    radioClass,
    className,
    containerClass,
    itemsContainerClass,
    labelClass,
}) => {


    return (
        <div className={cn("inputdiv", containerClass)}>
            <label className={cn(labelClass)} htmlFor={name}>{label}</label>
            <div className="w-full max-w-[550px]">
                <RadioGroup value={mainValue?.toString() || fallback || "unknown"} onValueChange={onChange} className={cn(position[arrangement!], itemsContainerClass)}>
                    {
                        options.map(({ value, name, icon }, index) => (
                            <div className={cn(radioVariants({ variant, size, className }), "flex items-center gap-2 w-full")} key={index} onClick={() => onclick(value as string)}>
                                <RadioGroupItem 
                                    checked={((value === "select") && ((String(mainValue).includes("TRANSFER"))) || (String(value) === mainValue)) ? true : false} 
                                    key={String(value)} value={String(value)} 
                                    className={cn(radioItemClass[variant || ''], radioClass)} 
                                    itemLabelClass={itemClass} 
                                    indicatorClass={radioIndicatorClass[variant || '']}
                                >
                                    <div className='flex items-center gap-1.5 mr-auto'>
                                        {icon && icon}
                                        {name}
                                    </div>
                                </RadioGroupItem>
                            </div>
                        ))
                    }
                </RadioGroup>
            </div>
        </div>
    );
};

export default RadioButtons;