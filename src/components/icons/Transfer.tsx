import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={12} cy={12} r={12} fill="white" fillOpacity={0.1} />
    <path
      d="M13.0287 11.3989C12.9201 11.3989 12.8116 11.3589 12.7258 11.2731C12.5601 11.1074 12.5601 10.8331 12.7258 10.6674L17.4116 5.98171C17.5773 5.81599 17.8516 5.81599 18.0173 5.98171C18.183 6.14742 18.183 6.42171 18.0173 6.58742L13.3316 11.2731C13.2516 11.3531 13.143 11.3989 13.0287 11.3989Z"
      fill="white"
    />
    <path
      d="M15.3316 11.8574H12.5716C12.3374 11.8574 12.1431 11.6631 12.1431 11.4288V8.66881C12.1431 8.43452 12.3374 8.24023 12.5716 8.24023C12.8059 8.24023 13.0002 8.43452 13.0002 8.66881V11.0002H15.3316C15.5659 11.0002 15.7602 11.1945 15.7602 11.4288C15.7602 11.6631 15.5659 11.8574 15.3316 11.8574Z"
      fill="white"
    />
    <path
      d="M13.7146 18.1431H10.286C7.18314 18.1431 5.85742 16.8174 5.85742 13.7146V10.286C5.85742 7.18314 7.18314 5.85742 10.286 5.85742H11.4289C11.6631 5.85742 11.8574 6.05171 11.8574 6.28599C11.8574 6.52028 11.6631 6.71456 11.4289 6.71456H10.286C7.65171 6.71456 6.71456 7.65171 6.71456 10.286V13.7146C6.71456 16.3489 7.65171 17.286 10.286 17.286H13.7146C16.3489 17.286 17.286 16.3489 17.286 13.7146V12.5717C17.286 12.3374 17.4803 12.1431 17.7146 12.1431C17.9488 12.1431 18.1431 12.3374 18.1431 12.5717V13.7146C18.1431 16.8174 16.8174 18.1431 13.7146 18.1431Z"
      fill="white"
    />
  </svg>
);
export default SVGComponent;
