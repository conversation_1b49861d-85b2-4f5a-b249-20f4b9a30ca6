import Link from 'next/link'
import React from 'react'
import Paybox from './Paybox'
import { cn } from '@/utils/classNames'

const Paybox360 = ({ className, tagLineClass }: { className?: string, tagLineClass?: string }) => {
  return (
    <Link href="/" className={cn('flex items-center gap-2', className)}>
      <Paybox />
      <h1 className="flex flex-col font-clash">
        <span className='font-bold text-[1.25rem]'>
          Paybox360
        </span>
        <span className={cn('text-xs', tagLineClass)}>by LibertyPay</span>
      </h1>
    </Link>
  )
}

export default Paybox360