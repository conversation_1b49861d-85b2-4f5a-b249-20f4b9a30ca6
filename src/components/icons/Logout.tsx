import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle opacity={0.2} cx={16} cy={16} r={16} fill="#FF1A1A" />
    <path
      d="M15.7333 18.8076C15.5749 18.8076 15.4166 18.7492 15.2916 18.6242C15.0499 18.3826 15.0499 17.9826 15.2916 17.7409L16.9833 16.0492L15.2916 14.3576C15.0499 14.1159 15.0499 13.7159 15.2916 13.4742C15.5333 13.2326 15.9333 13.2326 16.1749 13.4742L18.3083 15.6076C18.5499 15.8492 18.5499 16.2492 18.3083 16.4909L16.1749 18.6242C16.0583 18.7492 15.8916 18.8076 15.7333 18.8076Z"
      fill="white"
    />
    <path
      d="M17.8085 16.6758H9.3335C8.99183 16.6758 8.7085 16.3924 8.7085 16.0508C8.7085 15.7091 8.99183 15.4258 9.3335 15.4258H17.8085C18.1502 15.4258 18.4335 15.7091 18.4335 16.0508C18.4335 16.3924 18.1502 16.6758 17.8085 16.6758Z"
      fill="white"
    />
    <path
      d="M16 23.2923C15.6583 23.2923 15.375 23.009 15.375 22.6673C15.375 22.3257 15.6583 22.0423 16 22.0423C19.5583 22.0423 22.0417 19.559 22.0417 16.0007C22.0417 12.4423 19.5583 9.95898 16 9.95898C15.6583 9.95898 15.375 9.67565 15.375 9.33398C15.375 8.99232 15.6583 8.70898 16 8.70898C20.2917 8.70898 23.2917 11.709 23.2917 16.0007C23.2917 20.2923 20.2917 23.2923 16 23.2923Z"
      fill="white"
    />
  </svg>
);
export default SVGComponent;
