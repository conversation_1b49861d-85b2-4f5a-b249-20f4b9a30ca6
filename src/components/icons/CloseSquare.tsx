import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <g opacity={0.5}>
            <path
                d="M7.64147 12.9841C7.48314 12.9841 7.3248 12.9258 7.1998 12.8008C6.95814 12.5591 6.95814 12.1591 7.1998 11.9174L11.9165 7.20078C12.1581 6.95911 12.5581 6.95911 12.7998 7.20078C13.0415 7.44245 13.0415 7.84245 12.7998 8.08411L8.08314 12.8008C7.96647 12.9258 7.7998 12.9841 7.64147 12.9841Z"
                fill="currentColor"
            />
            <path
                d="M12.3581 12.9841C12.1998 12.9841 12.0415 12.9258 11.9165 12.8008L7.1998 8.08411C6.95814 7.84245 6.95814 7.44245 7.1998 7.20078C7.44147 6.95911 7.84147 6.95911 8.08314 7.20078L12.7998 11.9174C13.0415 12.1591 13.0415 12.5591 12.7998 12.8008C12.6748 12.9258 12.5165 12.9841 12.3581 12.9841Z"
                fill="currentColor"
            />
            <path
                d="M12.4998 18.9577H7.49984C2.97484 18.9577 1.0415 17.0243 1.0415 12.4993V7.49935C1.0415 2.97435 2.97484 1.04102 7.49984 1.04102H12.4998C17.0248 1.04102 18.9582 2.97435 18.9582 7.49935V12.4993C18.9582 17.0243 17.0248 18.9577 12.4998 18.9577ZM7.49984 2.29102C3.65817 2.29102 2.2915 3.65768 2.2915 7.49935V12.4993C2.2915 16.341 3.65817 17.7077 7.49984 17.7077H12.4998C16.3415 17.7077 17.7082 16.341 17.7082 12.4993V7.49935C17.7082 3.65768 16.3415 2.29102 12.4998 2.29102H7.49984Z"
                fill="currentColor"
            />
        </g>
    </svg>
);
export default SVGComponent;
