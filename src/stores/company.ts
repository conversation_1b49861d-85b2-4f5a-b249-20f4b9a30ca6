'use client'

import { TBranch } from '@/types/user'
import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

type Data = {
    company: string, branch: string, branchData: TBranch, first_name: string, last_name: string, username: string, sales_user_role:string
}
interface CompanyState {
    company: string
    branch: string
    branchData: TBranch,
    username: string,
    first_name: string,
    last_name: string,
    sales_user_role: string,

    setCompanyInfo: ({ company, branch, branchData, first_name, last_name, username, sales_user_role }: Data) => void
    clearCompanyInfo: () => void
}

export const useCompanyStore = create<CompanyState>()(
    persist(
        (set, get, api) => ({
            company: '',
            branch: '',
            username: '',
            first_name: '',
            last_name: '',
            sales_user_role: '',
            branchData: {} as TBranch,
            setCompanyInfo: ({ company, branch, branchData, first_name, last_name, username, sales_user_role }) => set({ company, branch, branchData, first_name, last_name, username, sales_user_role }),
            clearCompanyInfo: () => {
                set({ company: '', branch: '', first_name: '', last_name: '', username: '', branchData: {} as TBranch })
                api.persist.clearStorage()
            },
        }),
        {
            name: 'company-storage',
            storage: createJSONStorage(() => localStorage),
        }
    )
)