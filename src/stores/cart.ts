"use client";

import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { TCartItem, TProduct } from "@/app/(main)/dashboard/misc/types";

interface CartState {
  active_cart: TCartItem[];
  add_item_from_saved_sales: (item: TProduct) => void;
  add_item: (item: TProduct) => void;
  remove_item: (item_id: string) => void;
  update_item_quantity: (item_id: string, quantity: number) => void;
  update_item_discount: (item_id: string, discount_value: number) => void;
  delete_item: (item_id: string) => void;
  clear_cart: () => void;
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      active_cart: [],

      add_item: (item) => {
        set((state) => {
          const existingItem = state.active_cart.find(
            (cartItem) => cartItem.item_id === item.item_id
          );

          if (existingItem) {
            return {
              active_cart: state.active_cart.map((cartItem) =>
                cartItem.item_id === item.item_id
                  ? { ...cartItem, quantity: cartItem.quantity + 1 }
                  : cartItem
              ),
            };
          } else {
            return {
              active_cart: [...state.active_cart, { ...item, quantity: 1 }],
            };
          }
        });
      },
      add_item_from_saved_sales: (item) => {
        console.log(item);

        set((state) => {
          return {
            active_cart: [...state.active_cart, { ...item }],
          };
        });
      },
      remove_item: (item_id) => {
        set((state) => {
          const existingItem = state.active_cart.find(
            (cartItem) => cartItem.item_id === item_id
          );

          if (existingItem && existingItem.quantity > 1) {
            return {
              active_cart: state.active_cart.map((cartItem) =>
                cartItem.item_id === item_id
                  ? { ...cartItem, quantity: cartItem.quantity - 1 }
                  : cartItem
              ),
            };
          } else {
            return {
              active_cart: state.active_cart.filter(
                (cartItem) => cartItem.item_id !== item_id
              ),
            };
          }
        });
      },

      update_item_quantity: (item_id, quantity) => {
        set((state) => ({
          active_cart: state.active_cart
            .map((cartItem) =>
              cartItem.item_id === item_id
                ? { ...cartItem, quantity: Math.max(0, parseFloat(quantity.toString())) }
                : cartItem
            )
            .filter((cartItem) => cartItem.quantity > -0.1),
        }));
      },

      update_item_discount: (item_id, discount_value) => {
        set((state) => ({
          active_cart: state.active_cart.map(cartItem =>
            cartItem.item_id === item_id
              ? { ...cartItem, item_discount_value: Math.max(0, discount_value) }
              : cartItem
          )
        }));
      },

      delete_item: (item_id) => {
        set((state) => ({
          active_cart: state.active_cart.filter(
            (cartItem) => cartItem.item_id !== item_id
          ),
        }));
      },

      clear_cart: () => {
        set({ active_cart: [] });
      },
    }),
    {
      name: "cart-storage",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
